
#include <iostream>
#include <unistd.h>
#include "wdef.h"
#include "baUtil.hpp"
#include "client.h"
using namespace std;

static void loop();
static int loop_idle;

void centerLoop (int idle)
{
    loop_idle = idle;
    //p=p;
    cout<<"\n"<< __func__<<endl;;

    for(;;)
    {
      loop();
      sleep(2);
      fflush(stdout);

    }
    return;
}

void loop()
{
    if(loop_idle == BA_ROLE) return;

    else if(loop_idle == RELAY_ROLE)
    {
      if(!baUtil::isFirstAlive())
      {
          exit_process();
      }
    }
    else if(baUtil::isFirstAlive())
    {
        exit_process();
    }
}
