#include "modbusSoyal.hpp"

using namespace std;
modbusSoyal::modbusSoyal()
{
  this->doneThisPoll = false;
}

modbusSoyal::~modbusSoyal()
{

}

bool modbusSoyal::get_next_data(uint8_t*p_buf,int*p_len)
{
   return get_do_data(p_buf,p_len);
}
bool modbusSoyal::get_do_data(uint8_t *p_buf,int* p_len)
{
    if (this->doneThisPoll)
    {
      this->doneThisPoll = false;
      return false;
    }
    if(DO_count == 0)
    {
      return false;
    }

    if(modbusDev::set_do(p_buf,p_len))
    {
      cout<<"set do "<<endl;
      fflush(stdout);
      return true;
    }

    p_buf[0] = 0x7E;
    p_buf[1] = 0x05;
    p_buf[2] = device_id;
    p_buf[3] = 0x21;
    p_buf[4] = 0x00;

    p_buf[5] = p_buf[2]^0x21^0x00^0xFF;
    p_buf[6] = p_buf[2]+p_buf[4]+0x21+p_buf[5];
    *p_len = 7;
    this->doneThisPoll = true;
    return true;
}

bool modbusSoyal::get_di_data(uint8_t *p_buf,int* p_len)
{
    if(DI_count == 0)
    {
      return false;
    }

    return false;
    //cout<<"\nmodbusSoyal::get_di_data"<<endl;
    p_buf[0] = 0x7E;
    p_buf[1] = 0x05;
    p_buf[2] = DI_addr_start;
    p_buf[3] = 0x21;
    p_buf[4] = 0x00;

    p_buf[5] = p_buf[2]^0x21^0x00^0xFF;
    p_buf[6] = p_buf[2]+p_buf[4]+0x21+p_buf[5];
    *p_len = 7;

    return true;
}

void modbusSoyal::set_do(int addr,int index,char value,uint8_t*p_buf,int*p_len)
{
    cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;

    p_buf[0] = 0x7E;
    p_buf[1] = 0x05;
    p_buf[2] = addr;
    p_buf[3] = 0x21;
    p_buf[4] = 0x84;
    p_buf[5] = 0xFF^p_buf[2]^p_buf[3]^p_buf[4];
    p_buf[6] = p_buf[2]+p_buf[3]+p_buf[4]+p_buf[5];

    *p_len = 7;

}
