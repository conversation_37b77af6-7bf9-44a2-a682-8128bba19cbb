#include <ctime>
#include "waterMeterNodeOpcDaV2.hpp"

#include "baUtil.hpp"
#include "utility.hpp"

using namespace std;
#define MAX_RS485_DATA 15
waterMeterNodeOpcDaV2::waterMeterNodeOpcDaV2()
{
  accumulateWaterFlow = 0;
  decimal_digits = -1;
  update_timestamp = 0;
}

waterMeterNodeOpcDaV2::~waterMeterNodeOpcDaV2()
{
}

int waterMeterNodeOpcDaV2::set(Json::Value it)
{
  //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;
  analogNode::set(it);
  set_int_value(it, &m_id, "id");
  set_int_value(it, &m_dio_type, "dio_type");  
  set_double_value(it, &accumulateWaterFlow, "accumulateWaterFlow");
  int reg_arr[] =
      {
          215, // decimal digits
      };

  uint8_t send[MAX_RS485_DATA];
  uint16_t crc;


  send[0] = m_addr / 256;
  send[1] = m_addr % 256;
  send[2] = send[3] = send[4] = 0x00;
  send[5] = 0x06;

  send[6] = 0x01;
  send[7] = 0x03;
  send[8] = m_addr / 256;
  send[9] = m_addr % 256;

  send[10] = 0x00;
  send[11] = 0x02;
  RS485Msg msg;
  msg.setData(send, 12);
  m_msg.push_back(msg);


  iterator = m_msg.begin();

  return 1;
}

bool waterMeterNodeOpcDaV2::set_data(uint8_t *p_data, int len)
{
  if ((p_data[0]  != m_addr / 256) || (p_data[1] % 256 != m_addr % 256))
  {
    return false;
  }  
  uint8_t buf4[4];
  float float_value;
  switch (m_index)
  {
  case 0:
    buf4[0] = p_data[12];
    buf4[1] = p_data[11];
    buf4[2] = p_data[10];
    buf4[3] = p_data[9];
    accumulateWaterFlow = *(float *)&buf4[0];
    updateMessage();
    // volage = (int)(float_value * 100);
    break;
  }

  m_index++;

  if (m_index >= m_msg.size())
  {
    m_index = 0;
  }

  return true;
}
int waterMeterNodeOpcDaV2::getIndex()
{
  return index;
}
int waterMeterNodeOpcDaV2::getPid()
{
  return m_pid;
}
void waterMeterNodeOpcDaV2::updateMessage()
{
  int current_timestamp = static_cast<int>(time(NULL));
  if ( (current_timestamp > (update_timestamp + 1 * 60)))
  {
    update_timestamp = current_timestamp;
    static int s_cnt = MAX_UPDATEMESSAGE;
    bool is_show;

    is_show = false;
    
    if (++s_cnt > MAX_UPDATEMESSAGE)
    {
      s_cnt = 0;
      is_show = true;
    }
    stringstream ss;
    ss << "/index.php?option=\"com_floor&task=sroots.update_watermeter&accumulateWaterFlow=" << accumulateWaterFlow << "&id=" << m_id << "\" ";
    // cout << ss.str() << endl;
    WSendMsg msg;
    // cout << "opc_water_update : " << accumulateWaterFlow << endl;
    msg.set(ss.str(), "/tmp/sendrs485watermetermsg", "/tmp/SENDRS485WATERMETERMSG", true, false);
    baUtil::add_sendMessage(&msg);
  }
}