
#include <iostream>
#include <unistd.h>
#include "wdef.h"
#include "baUtil.hpp"
#include "client.h"
#include "utility.hpp"
#include "baUtil.hpp"
using namespace std;

static void idle_loop();
static void lookup_event(int fd);

static int loop_idle;
static bool m_run;
static int m_maxfd;

static fd_set m_fd_r;    /* set of fifo read filedescriptors for select() */
static fd_set m_fd_e;

static vec_int m_fds;

void rs485Loop (int idle)
{
    loop_idle = idle;
    //p=p;
    cout<<"\n"<< __func__<<endl;

    m_run = true;
    while(m_run)
    {
      idle_loop();
    }
    return;
}

void idle_loop()
{
     //int ret;
     FD_ZERO(&m_fd_r);
     FD_ZERO(&m_fd_e);

     m_maxfd = 0;

     struct timeval timeout;                // timeout for select()
     timeout.tv_usec = 500000;
     timeout.tv_sec = 0;
     time_t seconds;

     seconds = 0;
     m_run = true;
     while(m_run)
     {
          time_t tm = time(0);

          int pass_seconds = difftime(tm,seconds);

          if(pass_seconds)
          {
            main_timeout(pass_seconds,true);
            seconds = tm;

          }

           fd_set testfds_r;
           fd_set testfds_e;
          int ret;
          //printf("\n%s ",__func__);
          //timeout.tv_usec = 40000;

           testfds_r = m_fd_r;
           testfds_e = m_fd_e;
           ret = select(m_maxfd+1, &testfds_r, NULL, &testfds_e, &timeout);
           //printf("\n%s %d ",__func__,ret);
           if (ret < 0) {
    	         fprintf(stdout, "select()'s error was %s", strerror(ret));
               cout << " select() rs485"<<endl;
               fflush(stdout);
    	         //exit(EXIT_FAILURE);
               //m_run = false;
               continue;
            }

            if(ret == 0)
            {
                timeout.tv_usec = 500000;
                timeout.tv_sec = 0;
                continue;
            }
            else
            {
                int fd;
                for(fd=0;fd<m_maxfd+1;fd++)
                {
                    if (FD_ISSET(fd, &testfds_r))
                    {
                        lookup_event(fd);
                    }

                    if (FD_ISSET(fd, &testfds_e))
                    {
                        fprintf(stderr, "fd %d error",fd);

                    }

               }

             }

      }
}

void lookup_event(int fd)
{
    baUtil::find_device_and_process(fd,true);

}

void rs485_clearFdToSystem(int fd)
{
  FD_CLR(fd, &m_fd_r);
  FD_CLR(fd, &m_fd_e);

  if(fd == m_maxfd)
  {
      m_maxfd--;
  }

  cout<<" clear "<<fd<<" "<<m_maxfd<<endl;
  fflush(stdout);
}
void rs485_addFdToSystem(int fd)
{
  FD_SET(fd, &m_fd_r);
  FD_SET(fd, &m_fd_e);

  m_maxfd = max<int>(m_maxfd, fd);
  cout<<" add "<<fd<<" "<<m_maxfd<<endl;
  fflush(stdout);

}
