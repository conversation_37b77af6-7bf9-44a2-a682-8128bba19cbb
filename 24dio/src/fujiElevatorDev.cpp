#include "fujiElevatorDev.hpp"
#include "utility.hpp"
#include "baUtil.hpp"

using namespace std;
fujiElevatorDev::fujiElevatorDev()
{
  this->doneThisPoll = false;
  this->last_update_timestamp = 0;
}

fujiElevatorDev::~fujiElevatorDev()
{
}
// bool fujiElevatorDev::set_data(uint8_t *p_data, int len)
// {
//   bool ret;

//   ret = false;
//   //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;

//   for (auto &node : nodes)
//   {
//     if (node->m_device_id == p_data[0])
//     {
//       if (node->set_data(p_data, len))
//       {
//         ret = true;
//         break;
//       }
//     }
//   }

//   return ret;
// }
int fujiElevatorDev::set(Json::Value it)
{
  set_int_value(it, &db_id, "id");
  set_int_value(it, &device_id, "device_id");
  set_int_value(it, &dio_type, "dio_type");
  set_int_value(it, &m_addr, "addr");

  //cout<<"\n"<<it<<endl;
  cout << "ZZ" << it << endl;
  if (dio_type == 1)
  {
    doAddDIDevice(it);
  }
  else if (dio_type == 2)
  {
    doAddDODevice(it);
  }
  else
  {
    doAddDODevice(it);
  }

  auto it1 = it["nodes"];
  // cout << (sizeof(it["nodes"])/sizeof(&it["nodes"][0])) << endl;
  for (auto it2 : it1)
  {
    it2["pid"] = it["pid"];
    it2["did"] = it["id"];
    it2["addr"] = it["addr"];
    it2["port"] = it["port"];
    it2["pdio_type"] = it["dio_type"];
    // it2["m_device_id"] = it["device_id"];
    if (it2["enable"] == "1")
    {
      auto dev = std::make_shared<modbusNode>(); // modbusTCPDev dev;
      dev->set(it2);
      //std::cout << "elm: " << it ;//<< std::endl;

      nodes.push_back(dev);
    }
  }
  init_iter();

  return nodes.size();
}
bool fujiElevatorDev::get_next_data(uint8_t *p_buf, int *p_len)
{
  return get_di_data(p_buf, p_len);
}

bool fujiElevatorDev::get_di_data(uint8_t *p_buf, int *p_len)
{
  if (this->doneThisPoll)
  {
    this->doneThisPoll = false;
    return false;
  }
  // cout << DI_count << "liu DI:" << device_id << endl;
  if (DI_count == 0)
  {
    return false;
  }
  p_buf[0] = 0x40;
  p_buf[1] = 0x47;
  p_buf[2] = 0x45;
  p_buf[3] = 0x54;
  p_buf[4] = 0x20;
  p_buf[5] = 0x44; 
  p_buf[6] = 0x49;  
  p_buf[7] = 0x20;  
  p_buf[8] = 0x0d;
  *p_len = 9;
  this->doneThisPoll = true;
  return true;
}
void fujiElevatorDev::do_di(uint8_t *p_buf)
{
  int index = 1;
  for (auto &node : nodes)
  {    
    if (index == 1 || index == 2)
    {
      node->do_di(DI_addr_start, p_buf);
    }
    else
    {
      this->updateMessage(node->m_id, p_buf[2]);
    }
    index++;
  }
}

void fujiElevatorDev::updateMessage(int id, uint8_t floor_hex)
{
  int current_timestamp = static_cast<int>(time(NULL));
  if (current_timestamp > (last_update_timestamp + 3))
  {
    last_update_timestamp = current_timestamp;
    static int s_cnt = MAX_UPDATEMESSAGE;
    bool is_show;

    is_show = false;
    if (++s_cnt > MAX_UPDATEMESSAGE)
    {
      s_cnt = 0;
      is_show = true;
    }
    // cout << endl << endl ;
    // printf("%02x", floor_hex);
    //  cout <<endl <<endl;
    std::stringstream ss;
    ss << "/index.php?option=\"com_floor&task=sroots.update_analog&decimal_value=" <<  static_cast<int>(floor_hex) ;
    ss << "&id=" << id;
    ss << "&text_value=" << fujiElevatorFloorDisplayNames.find(floor_hex)->second << "\" ";
    // cout << "Floor:" << fujiElevatorFloorDisplayNames.find(floor_display_name)->second << endl;
    // ss << "/index.php?option=\"com_floor&task=sroots.update_fujielevator&id=" << id;
    // ss << "&floor_display_name=" << fujiElevatorFloorDisplayNames.find(floor_display_name)->second;
    // ss << "\" ";
    // // cout << ss.str() << endl;
    WSendMsg msg;
    // cout << ss.str() << endl;
    msg.set(ss.str(), "/tmp/sendrs485fujielevatormsg", "/tmp/SENDRS485fujielevatorMSG", true, is_show);
    baUtil::add_sendMessage(&msg);
  }
}