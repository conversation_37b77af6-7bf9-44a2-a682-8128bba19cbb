#include "modbusNode.hpp"
#include "utility.hpp"
#include "modbusDev.hpp"
#include "digitNode.hpp"
#include "tempNode.hpp"
#include "tempNodeYonGjiaWithLux.hpp"
#include "generalAiNode.hpp"
#include "tempNodePinron.hpp"
#include "tempNodeYonGjia.hpp"
#include "tempNodeYonGjia2.hpp"
#include "tempNodeYonGjia3In1.hpp"
#include "tempNodeYonGjia3In1Co.hpp"
#include "tempNodeTcs30a22.hpp"
#include "tempNodeTcs5282.hpp"
#include "coSensorNodeYonGjia.hpp"
#include "pmSensorNodeYonGjia.hpp"
#include "tempNodeJnc.hpp"
#include "elecNode.hpp"
#include "elecNodeCic.hpp"
#include "elecNodeDaePm210.hpp"
#include "elecNodeAcuvim.hpp"
#include "elecNodeTatung.hpp"
#include "elecNodeBenderPem333.hpp"
#include "elecNodeShihlin.hpp"
#include "irtiIvaPersonDetectionNode.hpp"
#include "irtiIvaPersonCountingNode.hpp"
#include "weemaIaqNode.hpp"
#include "elecNodeWeema1p.hpp"
#include "elecNodeWeema3p.hpp"
#include "elecNodeOpcDa.hpp"
#include "elecNodeOpcDaV2.hpp"
#include "elecNodeAemDrb.hpp"
#include "elecNodeBenderPem575.hpp"
#include "elecNodePrimevolt.hpp"
#include "elecNodeGeneralSolar.hpp"
#include "elecNodeShihlinSPM8Solar.hpp"
#include "elecNodeCicBaw1a2a.hpp"
#include "elecNodeCicBaw2c.hpp"
#include "elecNodeVmrMp7.hpp"
#include "elecNodeVmrMp8.hpp"
#include "elecNodeM4m.hpp"
#include "elecNodeKtMk3.hpp"
#include "coSensorNode.hpp"
#include "coSensorNodeGeneral.hpp"
#include "waterMeterNode.hpp"
#include "waterMeterNodeOpcDaV2.hpp"
#include "waterMeterNodeTkd.hpp"
#include "smartLampNode.hpp"
#include "jetecWindDirectionNode.hpp"
#include "jetecSoilMeterNode.hpp"
#include "jetecRainMeterNode.hpp"
#include "co2SensorNode.hpp"



using namespace std;
modbusNode::modbusNode()
{
  m_id = 0;

  m_DI_addr_start = 0;
  m_DI_count = 0;

  m_DO_addr_start = 0;
  m_DO_count = 0;

  m_p_dev = 0;
}

modbusNode::~modbusNode()
{

}

int modbusNode::get_did()
{
  return m_did;
}
int modbusNode::get_DO_addr_start()
{
  return m_DO_addr_start;
}

int modbusNode::set(Json::Value it)
{
  //std::cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<it<<std::endl;

  set_int_value(it,&m_id,"id");
  set_int_value(it,&m_did,"did");

  if(it["dio_type"] == "1")
  {
    doAddDIDevice(it);
  }
  else if(it["dio_type"] == "2")
  {
    doAddDODevice(it);
  }
  else
  {
    doAddDODevice(it);
  }

  int pdio_type;

  set_int_value(it,&pdio_type,"pdio_type");
  cout << "pdioType" << pdio_type;
  if(pdio_type == TEMP_DEVICE)
  {

      auto dev = std::make_shared<tempNode>();// modbusTCPDev dev;
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;

      nodes.push_back(dev);
  }
  else if(pdio_type == YON_GJIA_TEMP_DEVICE_WITH_LUX)
  {

      auto dev = std::make_shared<tempNodeYonGjiaWithLux>();// modbusTCPDev dev;
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;

      nodes.push_back(dev);
  }
  else if(pdio_type == YON_GJIA_TEMP_DEVICE)
  {

      auto dev = std::make_shared<tempNodeYonGjia>();// modbusTCPDev dev;
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;

      nodes.push_back(dev);
  }
  else if(pdio_type == YON_GJIA_TEMP2_DEVICE)
  {

      auto dev = std::make_shared<tempNodeYonGjia2>();// modbusTCPDev dev;
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;

      nodes.push_back(dev);
  }
  else if(pdio_type == YON_GJIA_TEMP_3_IN_1_DEVICE)
  {

      auto dev = std::make_shared<tempNodeYonGjia3In1>();// modbusTCPDev dev;
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;

      nodes.push_back(dev);
  }
  else if(pdio_type == YON_GJIA_TEMP_3_IN_1_DEVICE_CO)
  {

      auto dev = std::make_shared<tempNodeYonGjia3In1Co>();// modbusTCPDev dev;
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;

      nodes.push_back(dev);
  }
  else if(pdio_type == TCS30A22_TEMP_DEVICE)
  {

      auto dev = std::make_shared<tempNodeTcs30a22>();// modbusTCPDev dev;
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;

      nodes.push_back(dev);
  }
  else if(pdio_type == TCS5282_TEMP_DEVICE)
  {

      auto dev = std::make_shared<tempNodeTcs5282>();// modbusTCPDev dev;
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;

      nodes.push_back(dev);
  }
  else if(pdio_type == CO_SENSOR_YON_GJIA_DEVICE)
  {

      auto dev = std::make_shared<coSensorNodeYonGjia>();// modbusTCPDev dev;
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;

      nodes.push_back(dev);
  }
  else if(pdio_type == PM_SENSOR_YON_GJIA_DEVICE)
  {

      auto dev = std::make_shared<pmSensorNodeYonGjia>();// modbusTCPDev dev;
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;

      nodes.push_back(dev);
  }
  else if(pdio_type == JNC_TEMP_DEVICE)
  {

      auto dev = std::make_shared<tempNodeJnc>();// modbusTCPDev dev;
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;

      nodes.push_back(dev);
  }
  else if(pdio_type == PINRON_TEMP_DEVICE)
  {

      auto dev = std::make_shared<tempNodePinron>();// modbusTCPDev dev;
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;

      nodes.push_back(dev);
  }
  else if (pdio_type == WATER_METER_DEVICE) {
      auto dev = std::make_shared<waterMeterNode>();// modbusTCPDev dev;
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;
      cout << "WATER_METER_DEVICE" << m_id;
      nodes.push_back(dev);
  }
  else if (pdio_type == TKD_WATER_METER_DEVICE) {
      auto dev = std::make_shared<waterMeterNodeTkd>();// modbusTCPDev dev;
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;
      cout << "TKD_WATER_METER_DEVICE" << m_id;
      nodes.push_back(dev);
  }
  else if (pdio_type == CO_SENSOR_DEVICE) {
      auto dev = std::make_shared<coSensorNode>();// modbusTCPDev dev;
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;
      cout << "CO_SENSOR_DEVICE" << m_id;
      nodes.push_back(dev);
  }
  else if (pdio_type == CO_SENSOR_GENERAL_DEVICE) {
      auto dev = std::make_shared<coSensorNodeGeneral>();// modbusTCPDev dev;
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;
      cout << "CO_SENSOR_DEVICE" << m_id;
      nodes.push_back(dev);
  }
  else if (pdio_type == SMART_LAMP_DEVICE) {
      auto dev = std::make_shared<smartLampNode>();// modbusTCPDev dev;
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;
      cout << "SMART_LAMP_DEVICE" << m_id;
      nodes.push_back(dev);
  }
  else if(pdio_type == ELEC_DEVICE)
  {
      auto dev = std::make_shared<elecNode>();// modbusTCPDev dev;
      dev->set(it);
      // std::cout << "ELECNODEEEEEEEEEEEEEEEEEEEE: " << it ;//<< std::endl;

      nodes.push_back(dev);
  }
  else if(pdio_type == CIC_ELEC_DEVICE)
  {
      auto dev = std::make_shared<elecNodeCic>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == DAE_PM210_ELEC_DEVICE)
  {
      auto dev = std::make_shared<elecNodeDaePm210>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == ACUVIM_ELEC_DEVICE)
  {
      auto dev = std::make_shared<elecNodeAcuvim>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == SHIHLIN_ELEC_DEVICE)
  {
      auto dev = std::make_shared<elecNodeShihlin>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
    else if(pdio_type == WEEMA_IAQ_NODE)
  {
      auto dev = std::make_shared<weemaIaqNode>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == WEEMA_ELEC_DEVICE_1P)
  {
      auto dev = std::make_shared<elecNodeWeema1p>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == WEEMA_ELEC_DEVICE_3P)
  {
      auto dev = std::make_shared<elecNodeWeema3p>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == CIC_BAW1A2A_ELEC_DEVICE)
  {
      auto dev = std::make_shared<elecNodeCicBaw1a2a>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == CIC_BAW2C_ELEC_DEVICE)
  {
      auto dev = std::make_shared<elecNodeCicBaw2c>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == SHALUN_OPCDA_ELEC_DEVICE)
  {
      auto dev = std::make_shared<elecNodeOpcDa>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == GENERAL_OPCDA_ELEC_DEVICE)
  {
      auto dev = std::make_shared<elecNodeOpcDaV2>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == GENERAL_OPCDA_WATER_DEVICE)
  {
      auto dev = std::make_shared<waterMeterNodeOpcDaV2>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == AEM_DRB_ELEC_DEVICE)
  {
      auto dev = std::make_shared<elecNodeAemDrb>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == TATUNG_ELEC_DEVICE)
  {
      auto dev = std::make_shared<elecNodeTatung>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == PRIMEVOLT_SOLAR_POWER_METER_DEVICE)
  {
      auto dev = std::make_shared<elecNodePrimevolt>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == SHIHLIN_SPM8_SOLAR_POWER_METER_DEVICE)
  {
      auto dev = std::make_shared<elecNodeShihlinSPM8Solar>();// modbusTCPDev dev;
      dev->set(it);

      nodes.push_back(dev);
  }
  else if(pdio_type == GENERAL_SOLAR_DEVICE)
  {
      auto dev = std::make_shared<elecNodeGeneralSolar>();// modbusTCPDev dev;
      dev->set(it);

      nodes.push_back(dev);
  }
  else if(pdio_type == BENDER_PEM333_ELEC_DEVICE)
  {
      auto dev = std::make_shared<elecNodeBenderPem333>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == IRTI_IVA_PERSON_DETECTION_NODE)
  {
      auto dev = std::make_shared<irtiIvaPersonDetectionNode>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == IRTI_IVA_PERSON_COUNTING_NODE)
  {
      auto dev = std::make_shared<irtiIvaPersonCountingNode>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == BENDER_PEM575_ELEC_DEVICE)
  {
      auto dev = std::make_shared<elecNodeBenderPem575>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == VMR_MP7_ELEC_DEVICE)
  {
      auto dev = std::make_shared<elecNodeVmrMp7>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == VMR_MP8_ELEC_DEVICE)
  {
      auto dev = std::make_shared<elecNodeVmrMp8>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == M4M_ELEC_DEVICE)
  {
      auto dev = std::make_shared<elecNodeM4m>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == KT_MK3_ELEC_DEVICE)
  {
      auto dev = std::make_shared<elecNodeKtMk3>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == CO2_DEVICE)
  {
      auto dev = std::make_shared<co2SensorNode>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == JETEC_WIND_DIRECTION_DEVICE)
  {
      auto dev = std::make_shared<jetecWindDirectionNode>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == JETEC_RAIN_METER_DEVICE)
  {
      auto dev = std::make_shared<jetecRainMeterNode>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == JETEC_SOIL_METER_DEVICE)
  {
      auto dev = std::make_shared<jetecSoilMeterNode>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == JETEC_SOIL_METER_DEVICE)
  {
      auto dev = std::make_shared<jetecSoilMeterNode>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else if(pdio_type == GENERAL_AI_DEVICE)
  {
      auto dev = std::make_shared<generalAiNode>();// modbusTCPDev dev;
      dev->set(it);


      nodes.push_back(dev);
  }
  else
  {
      auto dev = std::make_shared<digitNode>();// modbusTCPDev dev;
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;
      nodes.push_back(dev);

  }


  init_iter();

  return nodes.size();
}

void modbusNode::init_iter()
{
  iterator = nodes.begin();
  for(auto& node : nodes)
  {
    node->init_iter();
  }
}
void modbusNode::doAddDIDevice(Json::Value it)
{
  set_int_value(it,&m_DI_addr_start,"addr");

  set_int_value(it,&m_DI_count,"port");



}
int modbusNode::getDoValue(int index) {

  for(auto& node : this->nodes)
  {
    if(node->getIndex() == index)
    {
      return node->get_do_state();
      // break;
    }
  }
}
void modbusNode::doAddDODevice(Json::Value it)
{

  //cout<<it<<endl;
  //fflush(stdout);
  set_int_value(it,&m_DO_addr_start,"addr");

  set_int_value(it,&m_DO_count,"port");


}

void modbusNode::do_di(int addr,uint8_t* p_buf)
{
  int index;
  index = m_DI_addr_start-addr;

  for(auto& node : nodes)
  {
    node->do_di(&p_buf[index]);
  }


}

void modbusNode::do_do(int addr,uint8_t* p_buf)
{
  int index;
  index = m_DO_addr_start-addr;
  //cout<<"\nmodbusNode::do_do "<<index <<" "<<m_DO_addr_start<<" "<<addr<<endl;
  for(auto& node : nodes)
  {
      node->do_do(&p_buf[index]);
  }


}


bool modbusNode::do_action(int parent,int device,int index,int value,int timeout)
{
  bool ret;

  cout<<"\n"<<typeid(*this).name()<<"::do_action "<<m_did<<endl;
  fflush(stdout);
  ret = false;
  if(device == m_did)
  {
    //printf("modbusNode::do_action  %02X ",std::addressof(m_p_dev));
    ((modbusDev*)m_p_dev)->do_doJob(m_DO_addr_start,index,value,timeout);

    ret = true;

  }

  return ret;

}

void modbusNode::setDIOValue(int index,int value)
{
  for(auto& node : nodes)
  {
    if(node->getIndex() == index)
    {
      node->setDIOValue(value);
      break;
    }
  }
}

bool modbusNode::get_next_data(uint8_t*p_buf,int*p_len)
{
  if(nodes.size() == 0)    return false;

  if(!check_iter_valid())
  {
    iterator = nodes.begin();
  }

  bool ret;

  auto ptr = iterator;
  ret = (*ptr)->get_next_data(p_buf,p_len);

  if(ret == false)
  {
    iterator++;
    if(iterator == nodes.end())
    {
      iterator = nodes.begin();

    }
    else
    {
      ptr = iterator;
      ret = (*ptr)->get_next_data(p_buf,p_len);

    }
  }


  return ret;

}

bool modbusNode::check_iter_valid()
{
  bool is_find = false;

  auto it = nodes.begin();

  for(;it != nodes.end();it++)
  {
    if(it == iterator)
    {
      is_find = true;
      break;
    }
  }

  return is_find;


}

bool modbusNode::set_data(uint8_t* p_data,int len)
{
  bool ret;

  ret = false;
  //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;

  for(auto& node : nodes)
  {
    if(node->set_data(p_data,len))
    {
      ret = true;
      break;
    }
  }

  return ret;

}
