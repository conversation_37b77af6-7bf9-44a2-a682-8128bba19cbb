#ifndef __ELECNODEPRIMEVOLT_HPP__
#define __ELECNODEPRIMEVOLT_HPP__
#include <json/json.h>

#include "analogNode.hpp"
#include "elecNode.hpp"
class elecNodePrimevolt : public elecNode
{
public:
  elecNodePrimevolt();
  virtual ~elecNodePrimevolt();

  int set(Json::Value value);
  bool set_data(uint8_t *p_data, int len);
  void setKWH(long kwh);
  long getKWH();

  // private:
  // public:

};

#endif
