#include "irtiIvaPersonCountingNode.hpp"
#include "baUtil.hpp"
#include "utility.hpp"
using namespace std;

irtiIvaPersonCountingNode::irtiIvaPersonCountingNode()
{
  decimal_value = 0;
  alarm_threshold = 65536;
  alarm_triggered = false;
}

irtiIvaPersonCountingNode::~irtiIvaPersonCountingNode()
{
}

int irtiIvaPersonCountingNode::set(Json::Value it)
{
  analogNode::set(it);

  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");
  set_int_value(it, &status, "status");

  set_int_value(it, &m_alarmdir, "dio_alarmdir");
  set_int_value(it, &m_alarm, "dio_alarm");

  float dec_value = 0;
  set_float_value(it, &dec_value, "decimal_value");
  set_int_value(it, &alarm_threshold, "alarm_threshold");
  decimal_value = (int)dec_value;

  // set_int_value(it, &m_temperature_alarmdir, "temperature_alarmdir");
  // set_int_value(it, &m_temperature_alarm, "temperature_alarm");
  // set_float_value(it, &m_temperature_alarm_threshold, "temperature_alarm_threshold");

  // cout << "irtiIvaPersonCountingNode::set" << it << endl;
  //    int m_humidity_alarmdir;
  //  int m_humidity_alarm;
  //  int m_temperature_alarmdir;
  //  int m_temperature_alarm;
  //  int m_humidity_alarm_threshold;
  //  int m_temperature_alarm_threshold;

  uint8_t send[12];
  send[0] = (m_addr-1) / 256;
  send[1] = (m_addr-1) % 256;
  send[2] = send[3] = send[4] = 0x00;
  send[5] = 0x06;
  send[6] = 0x01;
  send[7] = 0x03;
  send[8] = (1000 +m_addr*50+2) / 256;
  send[9] = (1000 +m_addr*50+2) % 256;
  send[10] = 0x00;
  send[11] = 0x0a;
  // send[0] = ;
  // send[1] = 0x03;
  // send[2] = 0;
  // send[3] = 0x00;
  // send[4] = 0;
  // send[5] = 0x02;
  // int crc;

  // crc = crc_chk(send, 6);
  // send[6] = crc % 256;
  // send[7] = crc / 256;

  RS485Msg msg;
  msg.setData(send, 12);
  m_msg.push_back(std::move(msg));

  iterator = m_msg.begin();

  return 1;
}
void irtiIvaPersonCountingNode::triggerAlarm()
{

  // cout << "cdecimal_value:" << decimal_value << ", alarm_threshold:" << alarm_threshold << ", alarm_triggerd:" << alarm_triggered << endl;
  if (decimal_value >= alarm_threshold)
  {
    status = 3;
    alarm_triggered = true;
    do_di_event(m_alarmdir, m_alarm);
  }
   else if (decimal_value < (alarm_threshold) && alarm_triggered)
  {
    status =1;    
    alarm_triggered = false;
    do_di_release(m_alarmdir, m_alarm);
  }

  // if (m_humidity >= m_humidity_alarm_threshold && !m_humidity_alarm_triggered)
  // {
  //   m_humidity_alarm_triggered = true;
  //   status = 3;
  //   // status = 3;
  //   // baUtil::do_alarm(dio_alarmdir_id, dio_alarm_id);
  //   do_di_event(m_humidity_alarmdir, m_humidity_alarm);
  //   // cout << "coSensor alarm" << endl;
  // }
  // else if (m_humidity < (0.95*m_humidity_alarm_threshold) && m_humidity_alarm_triggered)
  // {
  //   m_humidity_alarm_triggered = false;
  //   if (!m_temperature_alarm_triggered) {
  //     status =1;
  //   }
  //   // status = 1;
  //   // baUtil::clear_alarm(dio_alarmdir_id, dio_alarm_id);
  //   do_di_release(m_humidity_alarmdir, m_humidity_alarm);
  //   // cout << "coSensor clear alarm" << endl;
  // }

  // if (m_temp >= m_temperature_alarm_threshold && !m_temperature_alarm_triggered)
  // {
  //   m_temperature_alarm_triggered = true;
  //   status = 3;
  //   // status = 3;
  //   // baUtil::do_alarm(dio_alarmdir_id, dio_alarm_id);
  //   do_di_event(m_temperature_alarmdir, m_temperature_alarm);
  //   // cout << "coSensor alarm" << endl;
  // }
  // else if (m_temp < (0.95*m_temperature_alarm_threshold) && m_temperature_alarm_triggered)
  // {
  //   m_temperature_alarm_triggered = false;
  //   if (!m_humidity_alarm_triggered) {
  //     status = 1;
  //   }
  //   // status = 1;
  //   // baUtil::clear_alarm(dio_alarmdir_id, dio_alarm_id);
  //   do_di_release(m_temperature_alarmdir, m_temperature_alarm);
  //   // cout << "coSensor clear alarm" << endl;
  // }
  // if (get_di_alarm_triggered())
  // {
  //   status = 3;
  // }
  // else
  // {
  //   status = 1;
  // }
}
bool irtiIvaPersonCountingNode::get_di_alarm_triggered()
{
  return alarm_triggered;
  // return m_humidity_alarm_triggered || m_temperature_alarm_triggered;
}
DIOState irtiIvaPersonCountingNode::get_ediostate()
{
  if (status == 3)
  {
    return DIOState::DIO_ALARM;
  }
  else
  {
    return DIOState::DIO_IDLE;
  }
}
void irtiIvaPersonCountingNode::do_di_event(int alarmdir, int alarm)
{
  AEvent event;

  // event.name = m_name;
  // event.state = m_state;
  event.alarmdir = alarmdir;
  event.alarm = alarm;
  event.id = m_id;
  event.pid = 1;
  event.index = 0;
  event.status = get_ediostate();

  baUtil::do_di_event(&event);
}

void irtiIvaPersonCountingNode::do_di_release(int alarmdir, int alarm)
{
  AEvent event;

  // event.name = m_name;
  // event.state = m_state;
  event.alarmdir = alarmdir;
  event.alarm = alarm;
  event.id = m_id;
  event.pid = 1;
  event.index = 0;
  event.status = get_ediostate();

  baUtil::do_di_release(&event);
}
bool irtiIvaPersonCountingNode::set_data(uint8_t *p_data, int len)
{
  bool ret;

  ret = false;

  if (p_data[0] != ((m_addr-1) / 256) || p_data[1] != ((m_addr-1) % 256))
  {
    return false;
  }
  // float float_value;
  uint8_t buf[4];

  size_t target_addr = 9;
  buf[0] = p_data[target_addr + 1];
  buf[1] = p_data[target_addr];
  // buf[2] = p_data[target_addr + 3];
  // buf[3] = p_data[target_addr + 2];
  decimal_value = (int)*(int16_t *)&buf[0];

  updateMessage();
  // int temp = p_data[3] * 256 + p_data[4];
  // int humidity = (p_data[5] * 256 + p_data[6]) * humidity_weight;

  // // if (temp != m_temp || humidity != m_humidity)
  // {
  //   m_temp = temp /10.0;
  //   m_humidity = humidity;
  //   updateMessage();
  // }

  ret = true;

  return ret;
}

void irtiIvaPersonCountingNode::updateMessage()
{
  int current_timestamp = static_cast<int>(time(NULL));
  if (current_timestamp > (last_update_timestamp + 1))
  {
    last_update_timestamp = current_timestamp;
    triggerAlarm();
    sendMessageToBa();
  }
}
void irtiIvaPersonCountingNode::sendMessageToBa()
{
  static int s_cnt = MAX_UPDATEMESSAGE;
  bool is_show;

  is_show = false;
  if (++s_cnt > MAX_UPDATEMESSAGE)
  {
    s_cnt = 0;
    is_show = true;
  }
  std::stringstream ss;
  ss << "/index.php?option=\"com_floor&task=sroots.update_analog&decimal_value=" << decimal_value << "&id=" << m_id << "\" ";

  WSendMsg msg;

  msg.set(ss.str(), "/tmp/sendrs485irtiPersonDetection", "/tmp/SENDRS485IRTIPERSONDETECTION", true, is_show);
  baUtil::add_sendMessage(&msg);
}