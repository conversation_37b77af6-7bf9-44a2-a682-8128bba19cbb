#ifndef __COSENSORNODEGENERAL_HPP__
#define __COSENSORNODEGENERAL_HPP__
#include <json/json.h>

#include "analogNode.hpp"
class coSensorNodeGeneral : public analogNode
{
public:
    coSensorNodeGeneral();
    virtual ~coSensorNodeGeneral();

    int set(Json::Value value);
    bool set_data(uint8_t* p_data,int len);
    void sendMessageToBa();
    void triggerAlarm();
    void do_di_event();
    void do_di_release();
private:
  void updateMessage();
private:
  int m_id;
  int m_dio_type;

  // int m_temp;
  // int m_humidity;
  bool alarm_triggered;
  int dio_alarm_id;
  int dio_alarmdir_id;
  int alarm_threshold;
  int last_update_timestamp;
  int status;
  int m_co;
  uint8_t m_buf[10];
};

#endif
