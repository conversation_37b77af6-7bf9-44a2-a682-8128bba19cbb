
#ifndef __DIOJOB_HPP__
#define __DIOJOB_HPP__


class dioJob
{
public:
   dioJob();
   virtual ~dioJob();

    void set(int parent,int device,int addr,int index,int value,int timeout);
    void set(int parent,int device,int addr,int index,int value,int timeout, bool return_last_state, int last_state);
    bool cancel_timeout_job(int addr, int index);
    void do_timeout();
protected:

protected:


public:

  int m_parent;
  int m_device;
  bool m_is_set;
  int m_addr;
  int m_index;
  int m_value;
  int m_timeout;
  bool m_return_last_state;
  int m_last_state;
  bool m_cancelled;
};

#endif
