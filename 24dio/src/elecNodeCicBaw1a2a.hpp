#ifndef __ELECNODECICBAW1A2A_HPP__
#define __ELECNODECICBAW1A2A_HPP__
#include <json/json.h>

#include "analogNode.hpp"
#include "elecNode.hpp"
class elecNodeCicBaw1a2a : public elecNode
{
public:
  elecNodeCicBaw1a2a();
  virtual ~elecNodeCicBaw1a2a();

  int set(Json::Value value);
  bool set_data(uint8_t *p_data, int len);
  void setKWH(long kwh);
  long getKWH();

  // private:
  // public:

};

#endif
