#include "jetecSoilMeterNode.hpp"
#include "baUtil.hpp"
#include "utility.hpp"
using namespace std;

jetecSoilMeterNode::jetecSoilMeterNode()
{
  // m_co = -100;
  last_update_timestamp = 0;
  alarm_triggered = false;
  // alarm_threshold = 100;
  status = 1;
  // decimal_values = { 0 };
  text_value = "";
  index =0;
  // m_temp = 0;
  // m_humidity = 0;
}

jetecSoilMeterNode::~jetecSoilMeterNode()
{
}

int jetecSoilMeterNode::set(Json::Value it)
{
  analogNode::set(it);
  // cout<< endl << endl << endl << endl << endl;
  // cout << "jetecSoilMeterNode:" << it << endl;
  set_int_value(it, &m_id, "id");
  set_int_value(it, &index, "index");
  set_int_value(it, &m_dio_type, "dio_type");
  set_int_value(it, &dio_alarm_id, "dio_alarm");
  set_int_value(it, &dio_alarmdir_id, "dio_alarmdir");
  // set_int_value(it, &alarm_threshold, "co_sensor_alarm_threshold");
  set_int_value(it, &status, "status");
  // set_int_value(it, &int_value, "decimal_value");
  set_string_value(it, &text_value, "text_value");
  // cout << "coo sensodr" << it < endl;
  // if (status == 3) {
  //   alarm_triggered = true;
  // }
  uint8_t send[8];
  send[0] = m_addr;
  send[1] = 0x03;
  send[2] = (index-1)/256;
  send[3] = (index-1)%256;
  send[4] = 0x00;
  send[5] = 0x07;
  int crc;

  crc = crc_chk(send, 6);
  send[6] = crc % 256;
  send[7] = crc / 256;

  if (index == 1)
  {
    RS485Msg msg;
    msg.setData(send, 8);
    m_msg.push_back(std::move(msg));
  }

  iterator = m_msg.begin();

  return 1;
}

bool jetecSoilMeterNode::set_data(uint8_t *p_data, int len)
{
  bool ret;

  ret = false;
  if (p_data[0] == m_addr)
  {
    decimal_values[0] = floor((p_data[3] * 256 + p_data[4]) /65535.0*100.0);
    decimal_values[1] = floor((p_data[5] * 256 + p_data[6])/65535.0*100.0);
    decimal_values[2] = floor((p_data[7] * 256 + p_data[8])/65535.0*100.0);
    decimal_values[3] = floor((p_data[9] * 256 + p_data[10])/65535.0*100.0);
    decimal_values[4] = floor((p_data[11] * 256 + p_data[12])/65535.0*100.0);    
    decimal_values[5] = floor((p_data[13] * 256 + p_data[14])/65535.0*100.0);    
    updateMessage();
    sendMessageToBa();
    ret = true;

    //iterator++;
  }

  return ret;
}
// void jetecSoilMeterNode::updateValue(int v)
// {
//   int_value = v;
//   updateMessage();
// }
void jetecSoilMeterNode::updateMessage()
{
  int current_timestamp = static_cast<int>(time(NULL));
  if (current_timestamp > (last_update_timestamp + 5))
  {
    last_update_timestamp = current_timestamp;
    // triggerAlarm();
    sendMessageToBa();
  }
}
void jetecSoilMeterNode::sendMessageToBa()
{
  static int s_cnt = MAX_UPDATEMESSAGE;
  bool is_show;

  is_show = false;
  if (++s_cnt > MAX_UPDATEMESSAGE)
  {
    s_cnt = 0;
    is_show = true;
  }
  std::stringstream ss;
  ss << "/index.php?option=\"com_floor&task=sroots.update_jetec_soil_meter&id=" << m_id;

  for (size_t i = 0; i < 6; i++)
  {
    ss << "&" << urlencode("decimal_values[]") << "=" << decimal_values[i];
  }
  ss << "\" ";

  // cout << ss.str() << endl;
  WSendMsg msg;

  msg.set(ss.str(), "/tmp/sendrs485csoilmetermsg", "/tmp/SENDRS485SOILMETERMSG", true, is_show);
  baUtil::add_sendMessage(&msg);
}
