#ifndef __ELECNODEBENDERPEM575_HPP__
#define __ELECNODEBENDERPEM575_HPP__
#include <json/json.h>

#include "analogNode.hpp"
#include "elecNode.hpp"
class elecNodeBenderPem575 : public elecNode
{
public:
  elecNodeBenderPem575();
  virtual ~elecNodeBenderPem575();

  int set(Json::Value value);
  bool set_data(uint8_t *p_data, int len);
  void setKWH(long kwh);
  long getKWH();

  // private:
  // public:

};

#endif
