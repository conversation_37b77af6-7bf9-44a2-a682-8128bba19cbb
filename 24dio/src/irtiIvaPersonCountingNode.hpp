#ifndef __IRTIIVAPERSONCOUNTINGNODE_HPP__
#define __IRTIIVAPERSONCOUNTINGNODE_HPP__
#include <json/json.h>

#include "analogNode.hpp"
class irtiIvaPersonCountingNode : public analogNode
{
public:
    irtiIvaPersonCountingNode();
    virtual ~irtiIvaPersonCountingNode();

    int set(Json::Value value);
    bool set_data(uint8_t* p_data,int len);
    void triggerAlarm();
    void do_di_release(int alarmdir, int alarm);
    void do_di_event(int alarmdir, int alarm);
    DIOState get_ediostate();
    bool get_di_alarm_triggered();
// private:

protected:
  void updateMessage();
  void sendMessageToBa();
  // float humidity_weight;
// private:
  int m_id;
  int m_dio_type;

  int decimal_value;
  bool alarm_triggered;
  int m_alarmdir;
  int m_alarm;
  int alarm_threshold;
  int status = 1;
  int last_update_timestamp;

  uint8_t m_buf[10];
};

#endif
