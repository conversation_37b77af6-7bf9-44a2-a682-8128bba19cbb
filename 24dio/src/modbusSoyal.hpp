#ifndef __MODBUSSOYAL_HPP__
#define __MODBUSSOYAL_HPP__

#include "modbusDev.hpp"

class modbusSoyal : public modbusDev
{
public:
    modbusSoyal();
    virtual ~modbusSoyal();
    bool get_do_data(uint8_t *p_buf,int* p_len);
    bool get_di_data(uint8_t *p_buf,int* p_len);

    bool get_next_data(uint8_t*p_buf,int*p_len);
private:
    void set_do(int addr,int index,char value,uint8_t*p_buf,int*p_len);
    bool doneThisPoll;
};

#endif
