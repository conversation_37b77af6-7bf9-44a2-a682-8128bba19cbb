#include "coSensorNodeGeneral.hpp"
#include "baUtil.hpp"
#include "utility.hpp"
using namespace std;

coSensorNodeGeneral::coSensorNodeGeneral()
{
  m_co = -100;
  last_update_timestamp = 0;
  alarm_triggered = false;
  alarm_threshold = 100;
  status = 1;
  // m_temp = 0;
  // m_humidity = 0;
}

coSensorNodeGeneral::~coSensorNodeGeneral()
{
}

int coSensorNodeGeneral::set(Json::Value it)
{
  analogNode::set(it);
  //cout << "coSensorNodeGeneral:" << it << endl;
  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");
  set_int_value(it, &dio_alarm_id, "dio_alarm");
  set_int_value(it, &dio_alarmdir_id, "dio_alarmdir");
  set_int_value(it, &alarm_threshold, "co_sensor_alarm_threshold");
  set_int_value(it, &status, "status");
  // cout << "coo sensodr" << it < endl;
  if (status == 3) {
    alarm_triggered = true;
  }
  uint8_t send[20];

  send[0] = m_addr/256;
  send[1] = m_addr%256;
  send[2] = send[3] = send[4] = 0x00;
  send[5] = 0x06;
  send[6] = 0x01;
  send[7] = 0x03;
  send[8] = m_addr/256;
  send[9] = m_addr%256;
  send[10] = 0;
  send[11] = 0x01;
  // int crc;

  // crc = crc_chk(send, 6);
  // send[6] = crc % 256;
  // send[7] = crc / 256;

  RS485Msg msg;
  msg.setData(send, 12);
  m_msg.push_back(std::move(msg));

  iterator = m_msg.begin();

  return 1;
}

bool coSensorNodeGeneral::set_data(uint8_t *p_data, int len)
{
  bool ret;

  ret = false;
  if (p_data[0] == m_addr/256 && p_data[1] == m_addr %256)
  {
    // int temp = p_data[3]*256+p_data[4];
    // int humidity = p_data[5]*256+p_data[6];
    int newCo = p_data[9] * 256 + p_data[10]; // assign new co
    // cout << "cosensornodegeneral:" << newCo << ", " << alarm_threshold << endl;
    m_co = newCo;
    updateMessage();
    // if (newCo != m_co)
    // {
    // }
    // if(temp != m_temp || humidity != m_humidity)
    // {
    //   m_temp = temp;
    //   m_humidity = humidity;
    //   updateMessage();
    // }

    ret = true;

    //iterator++;
  }

  return ret;
}
void coSensorNodeGeneral::triggerAlarm()
{
  if (m_co >= alarm_threshold && !alarm_triggered)
  {
    alarm_triggered = true;
    status = 3;
    // baUtil::do_alarm(dio_alarmdir_id, dio_alarm_id);
    do_di_event();
    // cout << "coSensor alarm" << endl;
  }
  else if (m_co < alarm_threshold && alarm_triggered)
  {
    alarm_triggered = false;
    status = 1;
    // baUtil::clear_alarm(dio_alarmdir_id, dio_alarm_id);
    do_di_release();
    // cout << "coSensor clear alarm" << endl;
  }
}

void coSensorNodeGeneral::do_di_event()
{
  AEvent event;

  // event.name = m_name;
  // event.state = m_state;
  event.alarmdir = dio_alarmdir_id;
  event.alarm = dio_alarm_id;
  event.id = m_id;
  event.pid = 1;
  event.index = 0;
  event.status = DIOState::DIO_ALARM;

  baUtil::do_di_event(&event);
}
void coSensorNodeGeneral::do_di_release()
{
  AEvent event;

  // event.name = m_name;
  // event.state = m_state;
  event.alarmdir = dio_alarmdir_id;
  event.alarm = dio_alarm_id;
  event.id = m_id;
  event.pid = 1;
  event.index = 0;
  event.status = DIOState::DIO_IDLE;

  baUtil::do_di_release(&event);
}
void coSensorNodeGeneral::updateMessage()
{
  int current_timestamp = static_cast<int>(time(NULL));
  if (current_timestamp > (last_update_timestamp + 15))
  {
    last_update_timestamp = current_timestamp;
    triggerAlarm();
    sendMessageToBa();
  }
}
void coSensorNodeGeneral::sendMessageToBa()
{
  static int s_cnt = MAX_UPDATEMESSAGE;
  bool is_show;

  is_show = false;
  if (++s_cnt > MAX_UPDATEMESSAGE)
  {
    s_cnt = 0;
    is_show = true;
  }
  std::stringstream ss;
  ss << "/index.php?option=\"com_floor&task=sroots.update_cosensor&co=" << m_co << "&id=" << m_id << "\" ";
  // cout << ss.str() << endl;
  WSendMsg msg;

  msg.set(ss.str(), "/tmp/sendrs485cosensormsg", "/tmp/SENDRS485COSENSORMSG", true, true);
  baUtil::add_sendMessage(&msg);
}
