#ifndef __ELECNODEGENERALSOLAR_HPP__
#define __ELECNODEGENERALSOLAR_HPP__
#include <json/json.h>

#include "analogNode.hpp"
#include "elecNode.hpp"
class elecNodeGeneralSolar : public elecNode
{
public:
  elecNodeGeneralSolar();
  virtual ~elecNodeGeneralSolar();

  int set(Json::Value value);
  bool set_data(uint8_t *p_data, int len);
  void setKWH(long kwh);
  long getKWH();

  // private:
  // public:

};

#endif
