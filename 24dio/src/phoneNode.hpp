#ifndef __PHONENODE_HPP__
#define __PHONENODE_HPP__
#include "wdef.h"
#include <vector>
#include <json/json.h>
#include <fstream>

class phoneNode
{
public:
    phoneNode();
    virtual ~phoneNode();
    int set(Json::Value value);

    std::string getInfo();
    std::string getNote();
    int getStatus();
    void setStatus(int status);
    void writeToFile(EDIOState status, std::ofstream& file);

    bool update(std::string str,EDIOState status,map_int_string& ids_map);

    void main_timeout();
public:
  int online;
  int offline;

private:
    int id;
    std::string info;
    std::string note;

    int online_cnt;
    int offline_cnt;

    EDIOState state;
};

#endif
