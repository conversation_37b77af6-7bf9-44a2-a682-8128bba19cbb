#include <gtest/gtest.h>
#include "BaTest.hpp"
#include "Ba.hpp"
#include "baUtil.hpp"
#include "utility.hpp"
static BaTest ba;
Ba* g_pba;
#define DIO24DevFILETEST "/tmp/24DIOTEST"
#define AlarmFILE "/tmp/ALARMFILE"
#define IPTEST "/tmp/IPFILETEST"
#define PHONETEST "/tmp/PHONEFILETEST"
#define TIMINGTEST "/tmp/TIMINGFILETEST"
#define ConditionFILETEST "/tmp/CONDITIONFILETEST"
#define RS485FILETEST "/tmp/RS485FILETEST"
#define MSGCALLEEFILETEST "/tmp/MSGCALLEEFILETEST"

class DeviceTest : public testing::Test
{

protected:
    virtual void SetUp() override
    {
      g_pba = &ba;
#if 0
      baUtil::getAndSetDoorCardData();
      baUtil::getAndSetBaData();
      baUtil::getAndSetDIOData();

      baUtil::addAllTestCondition(ConditionFILETEST);
      baUtil::addAllTestAlarm(AlarmFILE);
      baUtil::addAllTestTiming(TIMINGTEST);
      //baUtil::getAndSetTimingData();
      baUtil::addAllTestIP(IPTEST);
      baUtil::addAllTestPhone(PHONETEST);
      baUtil::addAllTestCallee(MSGCALLEEFILETEST);
#endif
     baUtil::getBaData();
    }
    virtual void TearDown() override
    {
           // ...
    }

};
// 注意这里使用 TEST_F，而不是 TEST
TEST_F(DeviceTest, do_alarm)
{
  //  baUtil::do_alarm(40,52);


    ba.di_alarm(148,223,1);
    //ba.doPhoneDev();
#if 0
    for(int i=0;i<20;i++)
    {
        ba.do_dev_next_state();
        //ba.do_dev_recv_data();
    }
#endif

    char message[] = {
        0x32, 0x30, 0x27, 0x30, 0x38, 0x2F, 0x31, 0x32,
        0x20, 0x32, 0x30, 0x3A, 0x31, 0x37, 0x3A, 0x34,
        0x32, 0x20, 0x5B, 0x30, 0x30, 0x31, 0x2E, 0x31,
        0x37, 0x3A, 0x30, 0x42, 0x5D, 0x28, 0x30, 0x29,
        0x30, 0x31, 0x31, 0x37, 0x35, 0x3A, 0x33, 0x36,
        0x35, 0x36, 0x31, 0x20, 0x20, 0x20, 0x20, 0x20,
        0x20, 0x28, 0x4D, 0x31, 0x31, 0x29, 0x4E, 0x6F,
        0x72, 0x6D, 0x61, 0x6C, 0x20, 0x41, 0x63, 0x63,
        0x65, 0x73, 0x73, 0x0A,
     };

     if (__cplusplus == 201703L) std::cout << "C++17\n";
     else if (__cplusplus == 201402L) std::cout << "C++14\n";
     else if (__cplusplus == 201103L) std::cout << "C++11\n";
     else if (__cplusplus == 199711L) std::cout << "C++98\n";
     else std::cout << "pre-standard C++\n";

    is_valid_time(message);
    baUtil::doorCardEvent(message);


    AEvent event;

    event.msg_type = 1;
    event.msg_number = "0926740998";
    event.msg_name = "123";
    event.msg_context = "abc";

    baUtil::do_message(&event);

}

int main(int argc, char *argv[])
{
    ::testing::InitGoogleTest(&argc, argv);

    return RUN_ALL_TESTS();
}
