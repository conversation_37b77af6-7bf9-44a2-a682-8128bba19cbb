#include <ctime>
#include "elecNodeBenderPem575.hpp"

#include "baUtil.hpp"
#include "utility.hpp"

using namespace std;
#define MAX_RS485_DATA 10
elecNodeBenderPem575::elecNodeBenderPem575()
{
  kwh = 0;
  kw = 0;
  volage = 0;
  current = 0;
  freq = 0;
  power_factor = 0;

  update_kwh_timestamp = 0;
  update_timestamp = 0;

  old_kwh = 0;
}

elecNodeBenderPem575::~elecNodeBenderPem575()
{
}

int elecNodeBenderPem575::set(Json::Value it)
{
  //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;
  analogNode::set(it);
  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");

  int reg_arr[] =
      {
          200, // uint32 KWH
          30,   // kw total float w
          8,   // V1 (V A-N?) float v
          22,     // I Avg float a
          56,   // F (HZ) float hz
          54,   // PF float
          10,  // V2 (V BN?) float v
          12,   // V3 (V CN?) float v
          16,   // I1 (A A?) float a
          18,   // I2 (A B?) float a
          20,   // I3 (A C?) float a
          38    // qsum (kvar?) float var
      };
  uint8_t send[MAX_RS485_DATA];
  uint16_t crc;

  for (int i = 0; i < sizeof(reg_arr) / sizeof(reg_arr[0]); i++)
  {
    send[0] = m_addr;
    send[1] = 0x03;
    send[2] = reg_arr[i] / 256;
    send[3] = reg_arr[i] % 256;
    send[4] = 0;
    if (i == 0)
      send[5] = 0x04;
    else
      send[5] = 0x02;

    crc = crc_chk(send, 6);
    send[6] = crc % 256;
    send[7] = crc / 256;

    RS485Msg msg;
    msg.setData(send, 8);
    m_msg.push_back(msg);
  }

  iterator = m_msg.begin();

  baUtil::addElecNodes(this);

  return 1;
}

bool elecNodeBenderPem575::set_data(uint8_t *p_data, int len)
{
  if (p_data[0] != m_addr)
  {
    return false;
  }

  //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<m_index<<endl;
  //usleep(10000);
  // float val;
  // float *p_val;
  // int* int_val_1;
  unsigned int int_val;
  uint8_t buf[4];
  // int reg_arr[] =
  //     {
  //         206, // uint32 KWH
  //         30,   // kw total float w
  //         0,   // V1 (V A-N?) float v
  //         22,     // I Avg float a
  //         56,   // F (HZ) float hz
  //         54,   // PF float
  //         2,  // V2 (V BN?) float v
  //         4,   // V3 (V CN?) float v
  //         16,   // I1 (A A?) float a
  //         18,   // I2 (A B?) float a
  //         20,   // I3 (A C?) float a
  //         38    // qsum (kvar?) float var
  //     };
  buf[0] = p_data[6];
  buf[1] = p_data[5];
  buf[2] = p_data[4];
  buf[3] = p_data[3];
  // cout << "m_index:" << m_index << ",";
  // for (size_t i = 0; i < 4; i++)
  // {
  //   cout << static_cast<int>(p_data[6 - i]) << ",";
  // }
  // cout << endl
  //      << endl;
  // unsigned long t = 0;
  // long lt = 0;
  // short int short_i_v = 0;
  // int i = 0;
  // int_val = *(unsigned int *)&buf[0];
  float float_value = *(float *)&buf[0];
  switch (m_index)
  {
  case 0:  
    kwh = (long)(*(unsigned long *)&buf[0])*100;
    break;
  case 1:
    // lt = *(long *)&buf[0];
    kw = (int)(float_value/10);
    break;
  case 2:    
    volage = (int)(float_value*100);
    break;
  case 3:
    current = (int)(float_value *100);
    break;
  case 4:    
    freq = (int)(float_value*100);
    // freq = buf[0] * 256 + buf[1];
    break;
  case 5:    
    power_factor = (int)(float_value*100);
    // power_factor = buf[0] * 256 + buf[1];

    break;
  case 6:    
    V_BN = (int)(float_value*100);
    break;
  case 7:    
    V_CN = (int)(float_value*100);
    break;
  case 8:    
    A_A = (int)(float_value*100);
    break;
  case 9:    
    A_B = (int)(float_value*100);
    break;
  case 10:  
    A_C = (int)(float_value*100);

    break;
  case 11:   
    kvar = (int)float_value/10;
    updateMessage();
    break;
  }

  m_index++;

  if (m_index >= m_msg.size())
  {
    m_index = 0;
  }

  return true;
}