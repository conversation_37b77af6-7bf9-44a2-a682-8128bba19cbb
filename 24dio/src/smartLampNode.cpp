#include "smartLampNode.hpp"
#include "baUtil.hpp"
#include "utility.hpp"
using namespace std;

smartLampNode::smartLampNode()
{
}

smartLampNode::~smartLampNode()
{
}

int smartLampNode::set(Json::Value it)
{
  analogNode::set(it);

  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");
  set_int_value(it, &status, "status");
  set_float_value(it, &current, "smart_lamp_current");
  set_float_value(it, &voltage, "smart_lamp_voltage");
  set_float_value(it, &power, "smart_lamp_pwoer");
  set_uint64_value(it, &accumulatePower, "smart_lamp_accumulate_power");
  // set_int_value(it, &m_humidity_alarmdir, "humidity_alarmdir");
  // set_int_value(it, &m_humidity_alarm, "humidity_alarm");
  // set_int_value(it, &m_humidity_alarm_threshold, "humidity_alarm_threshold");
  // set_int_value(it, &m_temperature_alarmdir, "temperature_alarmdir");
  // set_int_value(it, &m_temperature_alarm, "temperature_alarm");
  // set_int_value(it, &m_temperature_alarm_threshold, "temperature_alarm_threshold");

  cout << "smartLampNode::set" << it << endl;
  //   int m_humidity_alarmdir;
  // int m_humidity_alarm;
  // int m_temperature_alarmdir;
  // int m_temperature_alarm;
  // int m_humidity_alarm_threshold;
  // int m_temperature_alarm_threshold;
  
  uint8_t send[8];
  send[0] = m_addr;
  send[1] = 0x03;
  send[2] = 0x10;
  send[3] = 0x00;
  send[4] = 0;
  send[5] = 0x0A;
  int crc;
// 0B 03 10 00 00 0A C1 A7
  crc = crc_chk(send, 6);
  send[6] = crc % 256;
  send[7] = crc / 256;

  RS485Msg msg;
  msg.setData(send, 8);
  m_msg.push_back(std::move(msg));

  iterator = m_msg.begin();

  return 1;
}
// void smartLampNode::triggerAlarm()
// {

//   if (m_humidity >= m_humidity_alarm_threshold && !m_humidity_alarm_triggered)
//   {
//     m_humidity_alarm_triggered = true;
//     status = 3;
//     // status = 3;
//     // baUtil::do_alarm(dio_alarmdir_id, dio_alarm_id);
//     do_di_event(m_humidity_alarmdir, m_humidity_alarm);
//     // cout << "coSensor alarm" << endl;
//   }
//   else if (m_humidity < m_humidity_alarm_threshold && m_humidity_alarm_triggered)
//   {
//     m_humidity_alarm_triggered = false;
//     if (!m_temperature_alarm_triggered) {
//       status =1;
//     }
//     // status = 1;
//     // baUtil::clear_alarm(dio_alarmdir_id, dio_alarm_id);
//     do_di_release(m_humidity_alarmdir, m_humidity_alarm);
//     // cout << "coSensor clear alarm" << endl;
//   }


//   if (m_temp >= m_temperature_alarm_threshold*10 && !m_temperature_alarm_triggered)
//   {
//     m_temperature_alarm_triggered = true;
//     status = 3;
//     // status = 3;
//     // baUtil::do_alarm(dio_alarmdir_id, dio_alarm_id);
//     do_di_event(m_temperature_alarmdir, m_temperature_alarm);
//     // cout << "coSensor alarm" << endl;
//   }
//   else if (m_temp < m_temperature_alarm_threshold*10 && m_temperature_alarm_triggered)
//   {
//     m_temperature_alarm_triggered = false;
//     if (!m_humidity_alarm_triggered) {
//       status = 1;
//     }    
//     // status = 1;
//     // baUtil::clear_alarm(dio_alarmdir_id, dio_alarm_id);
//     do_di_release(m_temperature_alarmdir, m_temperature_alarm);
//     // cout << "coSensor clear alarm" << endl;
//   }
//   // if (get_di_alarm_triggered())
//   // {
//   //   status = 3;
//   // }
//   // else
//   // {
//   //   status = 1;
//   // }
// }
// bool smartLampNode::get_di_alarm_triggered()
// {
//   return m_humidity_alarm_triggered || m_temperature_alarm_triggered;
// }
// DIOState smartLampNode::get_ediostate()
// {
//   if (status == 3)
//   {
//     return DIOState::DIO_ALARM;
//   }
//   else
//   {
//     return DIOState::DIO_IDLE;
//   }
// }
// void smartLampNode::do_di_event(int alarmdir, int alarm)
// {
//   AEvent event;

//   // event.name = m_name;
//   // event.state = m_state;
//   event.alarmdir = alarmdir;
//   event.alarm = alarm;
//   event.id = m_id;
//   event.pid = 1;
//   event.index = 0;
//   event.status = get_ediostate();

//   baUtil::do_di_event(&event);
// }

// void smartLampNode::do_di_release(int alarmdir, int alarm)
// {
//   AEvent event;

//   // event.name = m_name;
//   // event.state = m_state;
//   event.alarmdir = alarmdir;
//   event.alarm = alarm;
//   event.id = m_id;
//   event.pid = 1;
//   event.index = 0;
//   event.status = get_ediostate();

//   baUtil::do_di_release(&event);
// }
bool smartLampNode::set_data(uint8_t *p_data, int len)
{
  bool ret;

  ret = false;
  // if (len == 43)
  // {
  // } else 
  // {
  //   return false;
  // }
  if (p_data[0] == m_addr)
  {

    unsigned int crc = crc_chk(p_data, 41);
    if (p_data[41] != crc %256 || p_data[42] != crc / 256)
    {
      // cout << "smartLampL:::::::::::::::::" <<len << endl;
      return false;
    }
    // int temp = p_data[3] * 256 + p_data[4];
    bool valueChanged = false;
    // int humidity = p_data[5] * 256 + p_data[6];
    uint8_t temp1[4] =  {p_data[18],p_data[17],p_data[16],p_data[15]};
    memcpy(&temperature1, &temp1, sizeof(temperature1));  

    uint8_t temp2[4] = {p_data[22],p_data[21],p_data[20],p_data[19]};
    memcpy(&temperature2, &temp2, sizeof(temperature2));  

    uint8_t temp3[4] = {p_data[26],p_data[25],p_data[24],p_data[23]};
    memcpy(&temperature3, &temp3, sizeof(temperature3));  

    uint8_t temp4[4] = {p_data[30],p_data[29],p_data[28],p_data[27]};
    memcpy(&temperature4, &temp4, sizeof(temperature4));  

    uint8_t temp5[4] = {p_data[6],p_data[5],p_data[4],p_data[3]};
    memcpy(&power, &temp5, sizeof(power));
    
    float old_voltage = voltage;
    uint8_t temp6[4] = {p_data[10],p_data[9],p_data[8],p_data[7]};
    memcpy(&voltage, &temp6, sizeof(voltage));
    
    uint8_t temp7[4] = {p_data[14],p_data[13],p_data[12],p_data[11]};
    memcpy(&current, &temp7, sizeof(current));

    accumulatePower  = (uint64_t)p_data[31] << 56 | (uint64_t)p_data[32] << 48 | (uint64_t)p_data[33] << 40 | (uint64_t)p_data[34] << 32 | (uint64_t)p_data[35] << 24 | (uint64_t)p_data[36] << 16 | (uint64_t)p_data[37] << 8 | p_data[38];
    if (p_data[39] == 0xff && p_data[40] == 0xff)
    {
      status = 3;
    } else 
    {
      status = 1;
    }

    // if (old_voltage != voltage)
    // if (temp != m_temp || humidity != m_humidity)
    {
      // m_temp = temp;
      // m_humidity = humidity;
      updateMessage();
    }

    ret = true;

    //iterator++;
  }

  return ret;
}

void smartLampNode::updateMessage()
{
  int current_timestamp = static_cast<int>(time(NULL));
  if (current_timestamp > (last_update_timestamp + 30))
  {
    last_update_timestamp = current_timestamp;
    // triggerAlarm();
    sendMessageToBa();
  }
}
void smartLampNode::sendMessageToBa()
{
  static int s_cnt = MAX_UPDATEMESSAGE;
  bool is_show;

  is_show = false;
  if (++s_cnt > MAX_UPDATEMESSAGE)
  {
    s_cnt = 0;
    is_show = true;
  }
  
  std::stringstream ss;
  ss << "/index.php?option=\"com_floor&task=sroots.update_smartlamp&timestamp=" << last_update_timestamp << "&status=" << status << "&voltage=" << voltage << "&current=" << current << "&power=" << power << "&accumulatePower=" <<  accumulatePower <<"&id=" << m_id << "&temperature1=" << temperature1  << "&temperature2=" << temperature2  << "&temperature3=" << temperature3  << "&temperature4=" << temperature4 << "\" ";
  // ss << "/index.php?option=\"com_floor&task=sroots.update_smartlamp" << "&accumulatePower=" <<  accumulatePower <<"&id=" << m_id << "\" ";
  // cout << ss.str() << endl;
  WSendMsg msg;

  msg.set(ss.str(), "/tmp/sendrs485lampmsg", "/tmp/SENDRS485LAMPMSG", true, is_show);
  baUtil::add_sendMessage(&msg);
}