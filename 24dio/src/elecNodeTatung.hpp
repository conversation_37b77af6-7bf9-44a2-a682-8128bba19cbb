#ifndef __ELECNODETATUNG_HPP__
#define __ELECNODETATUNG_HPP__
#include <json/json.h>

#include "analogNode.hpp"
#include "elecNode.hpp"
class elecNodeTatung : public elecNode
{
public:
  elecNodeTatung();
  virtual ~elecNodeTatung();

  int set(Json::Value value);
  bool set_data(uint8_t *p_data, int len);
  void setKWH(long kwh);
  long getKWH();
private:
  long kwhs[12];
  // private:
  // public:

};

#endif
