#include "myAccount.hpp"
#include "utility.hpp"
#include "baUtil.hpp"

using namespace std;

myAccount::myAccount()
{

}

myAccount::~myAccount()
{
}

int myAccount::set(Json::Value it)
{
    //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<it<<endl;
    for(auto it1 : it)
    {
      //cout<<it1<<endl;
      set_string_value(it1,&username,"username");
      set_string_value(it1,&passwd,"passwd");
      set_string_value(it1,&outbound,"outbound");
      set_string_value(it1,&domain,"domain");
      set_int_value(it1,&port,"port");
      set_string_value(it1,&path,"note4");
      set_string_value(it1,&elec_list,"elec_list");
      set_float_value(it1,&elec_kw,"elec_alarm");

      set_int_value(it1,&low,"low");
      set_int_value(it1,&mid,"mid");
      set_int_value(it1,&high,"high");

      set_int_value(it1,&alarmdir_low,"alarmdir_low");
      set_int_value(it1,&alarmdir_mid,"alarmdir_mid");
      set_int_value(it1,&alarmdir_high,"alarmdir_high");

      set_int_value(it1,&alarm_low,"alarm_low");
      set_int_value(it1,&alarm_mid,"alarm_mid");
      set_int_value(it1,&alarm_high,"alarm_high");

      set_int_value(it1,&ftp_enable,"ftp_enable");
      set_string_value(it1,&ftp_server,"ftp_server");
      set_int_value(it1,&ftp_port,"ftp_port");
      set_string_value(it1,&ftp_user,"ftp_user");
      set_string_value(it1,&ftp_passwd,"ftp_passwd");

      set_string_value(it1,&sms_username,"sms_username");
      set_string_value(it1,&sms_passwd,"sms_passwd");

      set_int_value(it1,&rec_sec,"rec_sec");

      set_int_value(it1,&del_alarm_enable,"del_alarm_enable");
      set_int_value(it1,&del_alarmdir,"del_alarmdir");
      set_int_value(it1,&del_alarm,"del_alarm");

      set_int_value(it1,&limit_alarm_enable,"limit_alarm_enable");
      set_int_value(it1,&limit_alarmdir,"limit_alarmdir");
      set_int_value(it1,&limit_alarm,"limit_alarm");

      set_int_value(it1,&out_alarm_enable,"out_alarm_enable");
      set_int_value(it1,&out_alarmdir,"out_alarmdir");
      set_int_value(it1,&out_alarm,"out_alarm");

      set_int_value(it1,&passwd_error_alarm_enable,"passwd_error_alarm_enable");
      set_int_value(it1,&passwd_error_alarmdir,"passwd_error_alarmdir");
      set_int_value(it1,&passwd_error_alarm,"passwd_error_alarm");

      set_int_value(it1,&username_error_alarm_enable,"username_error_alarm_enable");
      set_int_value(it1,&username_error_alarmdir,"username_error_alarmdir");
      set_int_value(it1,&username_error_alarm,"username_error_alarm");

      break;
    }

    //cout<<"\n"<<ftp_enable<<" "<<ftp_server<<" "<<ftp_port<<" "<<ftp_user<<" "<<ftp_passwd<<endl;
    //baUtil::addElecMain(elec_list,elec_kw);

    return 1;
}
