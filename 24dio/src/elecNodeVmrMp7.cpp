#include <ctime>
#include "elecNodeVmrMp7.hpp"

#include "baUtil.hpp"
#include "utility.hpp"

using namespace std;
#define MAX_RS485_DATA 10
elecNodeVmrMp7::elecNodeVmrMp7()
{
  kwh = 0;
  kw = 0;
  volage = 0;
  current = 0;
  freq = 0;
  power_factor = 0;

  // timestamp = 0;
  update_kwh_timestamp = 0;
  update_timestamp = 0;
  old_kwh = 0;
}

elecNodeVmrMp7::~elecNodeVmrMp7()
{
}

int elecNodeVmrMp7::set(Json::Value it)
{
  //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;
  analogNode::set(it);
  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");

  // int reg_arr[] =
  //     {
  //         6799, //KWH kwh 1
  //         25,   // psum 2 (kw total?) kw 2
  //         7,   // V1 (V A-N?) v float 2
  //         57,     // I N-line 2
  //         51,   // F (HZ) hz float 2
  //         49,   // PF  2
  //         9,  // V2 (V BN?) v float
  //         11,   // V3 (V CN?) v float
  //         13,   // I1 (A A?) 2
  //         15,   // I2 (A B?) 2
  //         17,   // I3 (A C?) 2
  //         6801    // qsum (kvar?) kvar 2
  //     };
  int reg_arr[] =
      {
         7,
         49,
         6799
      };
      
  uint8_t send[MAX_RS485_DATA];
  uint16_t crc;

  for (int i = 0; i < sizeof(reg_arr) / sizeof(reg_arr[0]); i++)
  {
    send[0] = m_addr;
    send[1] = 0x03;
    send[2] = reg_arr[i] / 256;
    send[3] = reg_arr[i] % 256;
    send[4] = 0;
    if (i == 0)
    {
      send[5] = 20;
    }
    else if (i==1)
    {
      send[5] = 6;
    }
    else
    {
      send[5] = 4;
    }
    // if (i == 0)
    //   send[5] = 0x04;
    // else
    //   send[5] = 0x02;

    crc = crc_chk(send, 6);
    send[6] = crc % 256;
    send[7] = crc / 256;

    RS485Msg msg;
    msg.setData(send, 8);
    m_msg.push_back(msg);
  }

  iterator = m_msg.begin();

  baUtil::addElecNodes(this);

  return 1;
}

bool elecNodeVmrMp7::set_data(uint8_t *p_data, int len)
{
  if (p_data[0] != m_addr)
  {
    return false;
  }
  int aspect_length =7;  
  if (m_index == 0)
  {
    aspect_length = 43;
  }
  else if (m_index == 1)
  {
    aspect_length = 15;
  }
  else if (m_index == 2)
  {
    aspect_length = 11;
  }
  uint16_t crc = crc_chk(p_data, aspect_length);
  if (p_data[1] == 0x83 || p_data[1] != 0x03)
  {
    for (size_t i = 0; i < len; i++)
    {
      printf("%02X ", p_data[i]);
      /* code */
    }

    cout << "elec vmr mp7 response 83 error" << endl;
    m_index = 0;
    return true;
  }
  if (crc % 256 != p_data[aspect_length] || crc / 256 != p_data[aspect_length + 1])
  {
    for (size_t i = 0; i < len; i++)
    {
      printf("%02X ", p_data[i]);
      /* code */
    }

    cout << "elec vmr mp7 crc checksum error " << aspect_length << ", m_index:" << m_index << endl;
    m_index = 0;
    return true;
  }
  //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<m_index<<endl;
  //usleep(10000);
  // float val;
  // float *p_val;
  // int* int_val_1;
  int int_value;
  uint8_t buf[4];

  // buf[0] = p_data[6];
  // buf[1] = p_data[5];
  // buf[2] = p_data[4];
  // buf[3] = p_data[3];
  // cout << "m_index:" << m_index << ",";
  // for (size_t i = 0; i < 4; i++)
  // {
  //   cout << static_cast<int>(p_data[6 - i]) << ",";
  // }
  // cout << endl
  //      << endl;

  // int_value = (*(uint16_t *)&buf[0]) *100;
  switch (m_index)
  {
  case 0:
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    volage = (*(uint16_t *)&buf[0]) *100;
    buf[0] = p_data[10];
    buf[1] = p_data[9];
    buf[2] = p_data[8];
    buf[3] = p_data[7];
    V_BN = (*(uint16_t *)&buf[0]) *100;
    buf[0] = p_data[14];
    buf[1] = p_data[13];
    buf[2] = p_data[12];
    buf[3] = p_data[11];
    V_CN = (*(uint16_t *)&buf[0]) *100;
    buf[0] = p_data[18];
    buf[1] = p_data[17];
    buf[2] = p_data[16];
    buf[3] = p_data[15];
    A_A = (*(uint16_t *)&buf[0]) *100;
    buf[0] = p_data[22];
    buf[1] = p_data[21];
    buf[2] = p_data[20];
    buf[3] = p_data[19];
    A_B = (*(uint16_t *)&buf[0]) *100;
    buf[0] = p_data[26];
    buf[1] = p_data[25];
    buf[2] = p_data[24];
    buf[3] = p_data[23];
    A_C = (*(uint16_t *)&buf[0]) *100;

    buf[0] = p_data[42];
    buf[1] = p_data[41];
    buf[2] = p_data[40];
    buf[3] = p_data[39];
    kw = ((*(uint16_t *)&buf[0]) *100 )/1000;
    // kwh = (int)int_value;
    break;
  case 1:
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    power_factor = (*(uint16_t *)&buf[0]) *100;
    buf[0] = p_data[10];
    buf[1] = p_data[9];
    buf[2] = p_data[8];
    buf[3] = p_data[7];
    freq = (*(uint16_t *)&buf[0]) *100;
    buf[0] = p_data[22];
    buf[1] = p_data[21];
    buf[2] = p_data[20];
    buf[3] = p_data[19];
    current = (*(uint16_t *)&buf[0]) *100;
    break;
  case 2:
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    kwh = (*(uint16_t *)&buf[0]) *100;
    buf[0] = p_data[10];
    buf[1] = p_data[9];
    buf[2] = p_data[8];
    buf[3] = p_data[7];
    kvar = (*(uint16_t *)&buf[0]) *100;
    updateMessage();
    break;
  }

  m_index++;

  if (m_index >= m_msg.size())
  {
    m_index = 0;
  }

  return true;
}