#include "modbusTCP.hpp"
#include "utility.hpp"
#include "modbusSoyal.hpp"
#include "mitsubishiElevatorDev.hpp"
#include "fujiElevatorDev.hpp"
#include "liuchuanElevatorDev.hpp"
#include "yunyangFireFightingDev.hpp"
#include "baochungFireFightingDev.hpp"
using namespace std;

modbusTCP::modbusTCP()
{

}

modbusTCP::~modbusTCP()
{

}


void modbusTCP::addDevId(Json::Value it)
{
    int id;

    set_int_value(it,&id,"device_id");

    addDev(id,it);

}

void modbusTCP::addDev(int id,Json::Value it)
{
  bool ret;
  ret = false;
  for(auto& dev : devs)
  {
    if(dev->getDevId() == id)
    {
      ret = true;
      dev->set(it);
      break;
    }
  }
  //cout<<"\nvendor "<<m_vendor<<" "<<m_ipaddr<<endl;
  if(m_vendor == RS485_VENDOR)
  {
    //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<it<<endl;
  }


  if(ret == false)
  {
    //cout<<"\nnew device id "<<id<< " "<<m_vendor<<endl;
    if(m_vendor == SOYAL_VENDOR)
    {
      //cout<<it<<endl;
      auto dev = std::make_shared<modbusSoyal>();// modbusTCPDev dev;
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;
      devs.push_back(dev);

    }
    else if (m_vendor == YUNYANG_FIRE_FIGHTING_VENDOR)
    {
      auto dev = std::make_shared<yunyangFireFightingDev>();// modbusTCPDev dev;      
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;
      devs.push_back(dev);
    }
    else if (m_vendor == BAOCHUNG_FIRE_FIGHTING_VENDOR)
    {
      auto dev = std::make_shared<baochungFireFightingDev>();// modbusTCPDev dev;      
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;
      devs.push_back(dev);
    }
    else if (m_vendor == LIUCHUAN_ELEVATOR_VENDOR)
    {
      auto dev = std::make_shared<liuchuanElevatorDev>();// modbusTCPDev dev;      
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;
      devs.push_back(dev);
    }
    else if (m_vendor == MITSUBISHI_ELEVATOR_VENDOR)
    {
      auto dev = std::make_shared<mitsubishiElevatorDev>();// modbusTCPDev dev;      
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;
      devs.push_back(dev);
    }
    else if (m_vendor == FUJI_ELEVATOR_VENDOR)
    {
      auto dev = std::make_shared<fujiElevatorDev>();// modbusTCPDev dev;      
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;
      devs.push_back(dev);
    }
    else if (m_vendor == PANASONIC_TWO_LINE_VENDOR)
    {
      // cout <<"Panasoniccccc!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!c" << endl;
      auto dev = std::make_shared<modbusDev>(false, true);// modbusTCPDev dev;
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;
      devs.push_back(dev);
    }
    else
    {
      auto dev = std::make_shared<modbusDev>();// modbusTCPDev dev;
      dev->set(it);
      //std::cout << "elm: " << it ;//<< std::endl;
      devs.push_back(dev);

    }

  }

  iterator = devs.begin();


}

int modbusTCP::set(Json::Value it)
{
  //cout<<"set" <<it<<endl;
  ADevice::set(it);

  auto it1 = it["devices"];
  uint16_t header_id = 0;
  for (auto it2 : it1)
  {
      it2["pid"] = it["id"];
      it2["vendor"] = it["vendor"];
      it2["header_id"] = header_id++;
      if(it2["enable"] == "1")
          addDevId(it2);
  }

  init_iter();

  return devs.size();
}

void modbusTCP::init_iter()
{
  iterator = devs.begin();
  for(auto& dev : devs)
  {
    dev->init_iter();
  }
}
int modbusTCP::connect_device()
{
    if(devs.size() == 0)
    {
      return 0;
    }

    init_iter();

    return ADevice::connect_device();
}

bool modbusTCP::do_soyal_recv()
{

  if(m_buf[0] == 0x7E &&
     m_buf[2] == 0x00 &&
     m_buf[3] == 0x03 && 
     m_buf[1] == 0x0d
     )
     {
       
    // p_buf[0] = 0x7E;
    // p_buf[1] = 0x05;
    // p_buf[2] = device_id;
    // p_buf[3] = 0x21;
    // p_buf[4] = 0x00;

    // auto x = m_buf[2]^m_buf[3]^0x00^0xFF;
    // auto s = m_buf[2]+m_buf[4]+0x21+p_buf[5];

          uint8_t state = '0';
          if(m_buf[6]&0x02)
          {
              state = '1';
          }
          uint8_t buf[16];
          memset(buf,state,sizeof(buf));
          //cout<<"modbusSoyalDev::do_recv"<<endl;
          // do_soyal_recv_ex(m_buf[4],buf);
          // cout << "do_soyal: m_buf[4]:" << unsigned(m_buf[4]) << ", p_buf0~1: " << unsigned(buf[0]) << ", " << unsigned(buf[1]) << endl;
          do_recv_ex(0,m_buf[4],buf); 
          do_recv_ex(1,m_buf[4],buf); 
          // cout << " start do_recv_ex" << endl;
          // cout << " end_do_recv_ex" << endl;
   }
     else
     {
      //  return false;
       //cout <<__func__<<" "<<addr<<" "<<m_p_soyal->index<<" ";
#if 0
       for(int i=0;i<m_buf[1];i++)
       {
         printf("%02X ",m_buf[2+i]);
       }
       cout<<endl;
       fflush(stdout);
#endif
       //sleep(1);
     }

    return true;
}

bool modbusTCP::do_rs485_recv()
{
    if(check_iter_valid() == false)
    {
        init_iter();
    }

    for(auto& dev : devs)
    {
      if(dev->set_data(m_buf,13))
          break;
    }

    return true;
}

bool modbusTCP::do_recv()
{
    if(m_vendor == SOYAL_VENDOR)
    {
         return do_soyal_recv();
    }
    else if(m_vendor == RS485_VENDOR)
    {
        return do_rs485_recv();
    }
    else if (m_vendor == YUNYANG_FIRE_FIGHTING_VENDOR)
    {
      return do_yun_yang_fire_fighting_recv();
    }
    else if (m_vendor == BAOCHUNG_FIRE_FIGHTING_VENDOR)
    {
      return do_baochung_fire_fighting_recv();
    }
    else if (m_vendor == LIUCHUAN_ELEVATOR_VENDOR)
    {
      // cout << "recv" << endl;
        // return do_liuchuan_elevator_recv();
        return do_liuchuan_elevator_recv();
    }
    else if (m_vendor == MITSUBISHI_ELEVATOR_VENDOR)
    {
      // cout << "recv" << endl;
        // return do_liuchuan_elevator_recv();
        return do_mitsubishi_elevator_recv();
    }    
    else if (m_vendor == FUJI_ELEVATOR_VENDOR)
    {
      // cout << "recv" << endl;
        // return do_liuchuan_elevator_recv();
        return do_fuji_elevator_recv();
    }
    else
    {
        return do_normal_recv();
    }

}

bool modbusTCP::do_baochung_fire_fighting_recv()
{
  uint8_t diBuffer[1000];
  int resultIndex = 0;
  
  for (int size = 0; size <m_buf[2]; size+=2)
  {
   
    for (size_t i = 0; i < 8; i++)
    {
      size_t index = resultIndex*8 + i;
      if (m_buf[4+size] & (0x01<<i)) {
        diBuffer[index] = '1';
      } else
      {
        diBuffer[index] = '0';
      }
      // cout << "index:" << index << ", v:";
      // if (diBuffer[index] == '0')
      // {
      //   cout << "0  ";
      // } else 
      // {
      //   cout << "1  ";
      // }
    }
     resultIndex ++;;
  }
    
  // cout << endl;
  // for (size_t i = 0; i < 40; i++)
  // {
  //   if (diBuffer[i] == '0')
  //   {
  //     cout << i << ":0  ";
  //   } else 
  //   {
  //     cout << i << ":1  ";
  //   }
  //   //  printf("%02X ",diBuffer[i]);
  //   /* code */
  // }
  // cout << endl;
  // do_recv_ex(1, m_buf[0], diBuffer);
  do_multiple_addr_recv_ex(1, m_buf[0], (*iterator)->getDiStartAddress(),diBuffer);

  return true;
}
bool modbusTCP::do_yun_yang_fire_fighting_recv()
{
  // cout << "do_yun_yang_______:" << m_buf[2]/2;
  // cout << endl;
  uint8_t diBuffer[1000];
  int resultIndex = 0;
  // cout << "pre data:" ;
  // for (size_t i = 0; i <= m_buf[5] + 6; i++)
  // {
  //   /* code */
  //   printf("%02X", m_buf[i]);
  //   printf(", ");
  // }
  // cout << endl;  
  uint8_t m_buf_cp[50];
  for (size_t i = 0; i < m_buf[5]; i++)
  {
    m_buf_cp[i] = m_buf[6+i];
  } 
  // cout << "after data:" ;
  // for (size_t i = 0; i <= m_buf_cp[2] + 2; i++)
  // {
  //   /* code */
  //   printf("%02X", m_buf_cp[i]);
  //   printf(", ");
  // }
  // cout << endl;
  for (int size = 0; size < m_buf_cp[2]; size+=2)
  {
    int byteStartPosition = size;
    int byteEndPosition = size + 1;
    for (int bytePosition = byteEndPosition; bytePosition >= byteStartPosition; bytePosition--)
    {
      for (int shiftIndex = 0; shiftIndex < 8; shiftIndex+=2)
      {        
        string what = "";
        
        
        if ((m_buf_cp[bytePosition+3] & (0x03<<shiftIndex)) == (0x03<<shiftIndex))
        {
          diBuffer[resultIndex] = '1';
          diBuffer[resultIndex+1] = '0';
          what = "10";
        }
        else if ((m_buf_cp[bytePosition+3] & (0x01<<shiftIndex)) == (0x01<<shiftIndex)) {        
          diBuffer[resultIndex] = '1';
          diBuffer[resultIndex+1] = '0';
          what = "10-";
        }
        else if ((m_buf_cp[bytePosition+3] & (0x02<<shiftIndex)) == (0x02<<shiftIndex)) 
        {
          diBuffer[resultIndex] = '0';
          diBuffer[resultIndex+1] = '1';
          what = "01";
        }
        else if ((m_buf_cp[bytePosition+3] & (0x00<<shiftIndex)) == (0x00<<shiftIndex)) {
          diBuffer[resultIndex] = '0';
          diBuffer[resultIndex+1] = '0';
          what = "00";
        }
        else 
        {
          // cout << "WRONG!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!,";
        }
        // if (m_buf_cp[bytePosition+3] != 0x00)
        // {
        //     cout <<" size:"<< size ;
        //     cout << ", bytePosition:" << bytePosition ;
        //     cout << ", resultIndex:" << resultIndex ;
        //     cout << ", shiftIndex:" << shiftIndex ;
        //     cout << ", di_start_addr:" << (*iterator)->getDiStartAddress();
        //     cout << ", m_buf_cp[bytePosition+3]: " ;
        //     printf("%02X", m_buf_cp[bytePosition+3]);
        //     cout <<", what:" << what ;
        //     cout << endl;
        // }
        resultIndex +=2;
      }
    }
  }
  
  // cout << endl;
  // for (size_t i = 0; i < 32; i++)
  // {
  //    printf("%02X ",diBuffer[i]);
  //   /* code */
  // }
  // cout << endl;
  // do_recv_ex(1, m_buf[0], diBuffer);
  do_multiple_addr_recv_ex(1, m_buf_cp[0], (*iterator)->getDiStartAddress(),diBuffer);

  return true;
}
bool modbusTCP::do_fuji_elevator_recv()
{
  for(auto& node : devs)
  {
    uint8_t diBuffer[3];

    // for (size_t i = 0; i < (*iterator)->getDiStartAddress(); i++)
    // {
      
    // }
    uint8_t offset = 4-1;
    diBuffer[0] = m_buf[(*iterator)->getDiStartAddress() + offset];
    diBuffer[1] = m_buf[(*iterator)->getDiStartAddress() + offset + 1];
    diBuffer[2] = 0;
    // cout <<" fuji" << endl;
    // offset+=3;
    for (size_t i = 0; i < 5; i++)
    {
      // uint8_t t = m_buf[m_buf[(*iterator)->getDiStartAddress() + offset + 1 + i] == '1' ? 1 : 0;
      uint8_t t = ((m_buf[(*iterator)->getDiStartAddress() + offset + 2 + i] == '1' ? 1 : 0) << (i));
      diBuffer[2] = diBuffer[2] + t;
      // cout << static_cast<int>(t) << ", ";
      /* code */
    }
    // cout << endl << endl;
    // cout << static_cast<int>(diBuffer[2]) << endl << endl;

    // cout << " fuji end " << endl;
    // diBuffer[2] = 
    do_recv_ex(1, (*iterator)->getDiStartAddress(), diBuffer);
  }

  return true;
}
bool modbusTCP::do_mitsubishi_elevator_recv()
{
    uint8_t diBuffer[16];

    diBuffer[0] = m_buf[9] & 0x3f;
    diBuffer[1] = m_buf[9] & (0x01 <<6) ? '1' : '0';
    diBuffer[2] = m_buf[9] & (0x01 <<7) ? '1' : '0';
    diBuffer[3] = m_buf[10] & (0x01 ) ? '1' : '0';
    diBuffer[4] = m_buf[10] & (0x01 <<1) ? '1' : '0';
    do_recv_ex(1, (*iterator)->getDiStartAddress(), diBuffer);
    return true;
}
bool modbusTCP::do_liuchuan_elevator_recv()
{
    uint16_t sum = 0;
    uint8_t xordata = 0x00;
    for (int i = 2; i <= 44; i++)
    {
      sum = sum + m_buf[i];
    }
    for (int i = 2; i <= 44; i++)
    {
      xordata = xordata ^ m_buf[i];
    }
    if (m_buf[45] != sum / 256 
      || m_buf[46] != sum%256 
      || m_buf[47] != xordata)
    {
      // cout << "liuchung checksum error:" <<  static_cast<int>(m_buf[45]) <<"," <<  static_cast<int>(sum/256) << endl;
      // cout <<  static_cast<int>(m_buf[46]) << "," <<  static_cast<int>(sum%256) << endl;
      // cout <<  static_cast<int>(m_buf[47]) << "," <<  static_cast<int>(xordata) << endl;
      return true;
    }    


    uint8_t diBuffer[18];
    diBuffer[0] = m_buf[4];
    for(int j=0;j<6;j++)
    {
        diBuffer[j+1] = '0';
        if((m_buf[5]&(0x01<<j)))
        {
            diBuffer[j+1] = '1';
        }
    }
    for(int j=0;j<7;j++)
    {
        diBuffer[j+7] = '0';
        if((m_buf[6]&(0x01<<j)))
        {
            diBuffer[j+7] = '1';
        }
    }
    for(int j=0;j<4;j++)
    {
        diBuffer[j+14] = '0';
        if((m_buf[7]&(0x01<<j)))
        {
            diBuffer[j+14] = '1';
        }
    }
    int slave = m_buf[1];
    string failure = "";
    if (m_buf[7]&(0x01<<2))
    {
      failure = "fa";
    } else
    {
      failure = "no";
    }
    // cout << "Slave:" << slave << ", ";
    // cout << "Go Up：" << diBuffer[1]  << ", ";
    // cout << "GDown：" << diBuffer[2]  << ", ";
    // cout << "Opera：" << diBuffer[3]  << ", ";
    // cout << "Delay："  << diBuffer[14]  << ", ";
    // cout << "Parki："  << diBuffer[15]  << ", ";
    // cout << "Failu："  << diBuffer[16]  << "," << failure << m_buf[7] << ", ";
    // cout << "Floor：" << liuChuanElevatorFloorDisplayNames[diBuffer[0]] << endl;
    do_recv_ex(1, m_buf[1], diBuffer);
    return true;
}
bool modbusTCP::do_normal_recv()
{
 
    int dio_type;
    int i,j;
#if 0
    cout<<endl;
    for(i=0;i<11;i++)
    {
      printf("%02X ",m_buf[i]);
    }

    cout<<endl;
    fflush(stdout);
#endif
    if(m_buf[7] != 1 && m_buf[7] != 2)
    {
      cout<<__func__<<" "<<m_ipaddr<<endl;

      for(i=0;i<11;i++)
      {
            printf("%02X ",m_buf[i]);
      }

      cout<<endl;

      fflush(stdout);
      return true;
    }
    //memset(buf,0,1000);
    //cout<<"do_modbus_recv "<<(int)m_buf[7]<<" "<<(int)m_buf[8]<<" "<<(int)m_buf[9]<<" "<<(int)m_buf[10]<<endl;
    if(m_buf[7] == 1)
    {
        dio_type = 0;
#if 0
        cout<<__func__<<" 0 "<<m_ipaddr<<endl;
        fflush(stdout);
#endif
    }
    else if(m_buf[7] == 2)
    {
#if 0
      cout<<__func__<<" 1 "<<m_ipaddr<<endl;
      fflush(stdout);
#endif
        dio_type = 1;
    }
    uint8_t buf[m_buf[8]*8+50];

    int tmp = m_buf[8]*8;

    buf[tmp] = 0;
    for(i=0;i<m_buf[8];i++)
    {
      for(j=0;j<8;j++)
      {
          buf[i*8+j] = '0';
          if((m_buf[9+i]&(0x01<<j)))
          {
              buf[i*8+j] = '1';

          }
      }

    }
    if ((*iterator)->useCoilForDi)
    {
      if ((*iterator)->getEState() == EState::GET_DO_STATE)
      {

        do_recv_ex(1,m_buf[6],buf);
      }
      else if ((*iterator)->getEState() == EState::INVALID_STATE)
      {
        do_recv_ex(0,m_buf[6],buf);
      }
    }
    else
    {
      do_recv_ex(dio_type,m_buf[6],buf);
    }

    return true;
}

void modbusTCP::do_state()
{
//  if(this->getFd() <= 0) return;

  if(devs.size() == 0)    return;

  if(check_iter_valid() == false)
  {
     init_iter();
  }

  uint8_t buf[100];
  bool ret;

  ret = false;

  int len;
  len = 0;
  ret = (*iterator)->get_next_data(buf,&len);

  if(ret == false)
  {
    iterator++;
    if(iterator == devs.end())
    {
      init_iter();

    }

    ret = (*iterator)->get_next_data(buf,&len);

    if(ret == false)    return;
    //cout<<"\n"<<typeid(*this).name()<<__func__<<" "<<ret<<endl;
  }
  else
  {
    //cout<<"\n"<<typeid(*this).name()<<__func__<<" "<<__LINE__<<endl;
  }

    send_data(buf,len);

}

bool modbusTCP::cancel_previous_timeout_job(int parent, int device, int index)
{
  bool ret;
  ret = false;

  cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<m_id<< ", " << parent << ", " << index <<endl;
  fflush(stdout);
  if(m_id != parent) return false;

  for(auto& node : devs)
  {
    if(node->cancel_previous_timeout_job(parent,device,index))
    {
      // cout << "modbusTCP::do_action: " << last_state << endl;
      ret = true;
      break;
    }
  }

  return ret;
}
bool modbusTCP::do_action(int parent,int device,int index,int value,int timeout,bool return_last_state)
{
  bool ret;
  ret = false;

  cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<m_id<< ", " << parent << ", " << device << ", " << value << ", " << index << endl;
  fflush(stdout);
  if(m_id != parent) return false;

  for(auto& node : devs)
  {
    if(node->do_action(parent,device,index,value,timeout,return_last_state))
    {
      // cout << "modbusTCP::do_action: " << last_state << endl;
      ret = true;
      break;
    }
  }

  return ret;

}
bool modbusTCP::do_action(int parent,int device,int index,int value,int timeout)
{
  bool ret;
  ret = false;

  cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<m_id<<endl;
  fflush(stdout);
  if(m_id != parent) return false;

  for(auto& node : devs)
  {
    if(node->do_action(parent,device,index,value,timeout))
    {
      ret = true;
      break;
    }
  }

  return ret;

}

bool modbusTCP::check_iter_valid()
{
  bool is_find = false;

  auto it = devs.begin();

  for(;it != devs.end();it++)
  {
    if(it == iterator)
    {
      is_find = true;
      break;
    }
  }

  return is_find;


}

 void modbusTCP::do_multiple_addr_recv_ex(int dio_type,int id,int addr, uint8_t *p_buf)
 {
   if(check_iter_valid() == false)
   {
     init_iter();
   }

   if((*iterator)->getDiStartAddress() != addr)
   {
     iterator = devs.begin();
     while(iterator != devs.end())
     {
         if((*iterator)->getDiStartAddress() == addr)
         {
           break;
         }

         ++iterator;

     }

   }

   if(iterator == devs.end())    return;

   if(dio_type == 1)
   {

      (*iterator)->do_di(p_buf);
               //cout<<endl;
   }
   else if(dio_type == 0)
   {
      (*iterator)->do_do(p_buf);
   }

 }
  void modbusTCP::do_soyal_recv_ex(int id,uint8_t *p_buf)
 {
   if(check_iter_valid() == false)
   {
     init_iter();
   }

   if((*iterator)->getDevId() != id)
   {
     iterator = devs.begin();
     while(iterator != devs.end())
     {
         if((*iterator)->getDevId() == id)
         {
           break;
         }

         ++iterator;

     }

   }

   if(iterator == devs.end())    return;

    (*iterator)->do_di(p_buf);
    (*iterator)->do_do(p_buf);
 }

 void modbusTCP::do_recv_ex(int dio_type,int id,uint8_t *p_buf)
 {
   if(check_iter_valid() == false)
   {
     init_iter();
   }

   if((*iterator)->getDevId() != id)
   {
     iterator = devs.begin();
     while(iterator != devs.end())
     {
         if((*iterator)->getDevId() == id)
         {
           break;
         }

         ++iterator;

     }

   }

   if(iterator == devs.end())    return;

   if(dio_type == 1)
   {

      (*iterator)->do_di(p_buf);
               //cout<<endl;
   }
   else if(dio_type == 0)
   {
      (*iterator)->do_do(p_buf);
   }

 }


 void modbusTCP::main_timeout()
 {
   for(auto& dev : devs)
   {
     dev->main_timeout();
   }
 }

