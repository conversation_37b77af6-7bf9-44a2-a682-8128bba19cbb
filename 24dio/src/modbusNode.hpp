#ifndef __MODBUSNODE_HPP__
#define __MODBUSNODE_HPP__

#include <memory>
#include <vector>
#include <json/json.h>
#include "ANode.hpp"

class modbusNode
{
public:
    modbusNode();
    virtual ~modbusNode();
    int set(Json::Value value);
    void do_di(int addr,uint8_t* p_buf);
    void do_do(int addr,uint8_t* p_buf);

    virtual bool do_action(int parent,int device,int index,int value,int timeout);


    int get_did();
    int get_DO_addr_start();

    void setDIOValue(int index,int value);
    virtual int getDoValue(int index);
    virtual bool get_next_data(uint8_t*p_buf,int*p_len);
    virtual bool set_data(uint8_t* p_data,int len);

    void init_iter();
    int m_id;
private:
    int m_did;
    int m_device_id;
    int m_DI_addr_start;
    int m_DI_count;

    int m_DO_addr_start;
    int m_DO_count;

    void* m_p_dev;
private:

    std::vector<std::shared_ptr<ANode>> nodes;
    std::vector<std::shared_ptr<ANode>>::iterator iterator;

private:
    void doAddDIDevice(Json::Value it);
    void doAddDODevice(Json::Value it);

    bool check_iter_valid();
};

#endif
