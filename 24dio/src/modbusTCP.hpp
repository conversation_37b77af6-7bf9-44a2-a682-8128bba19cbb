#ifndef __MODBUSTCP_HPP__
#define __MODBUSTCP_HPP__

#include <iterator>
#include <vector>
#include <json/json.h>
#include "ADevice.hpp"
#include "digitNode.hpp"
#include "modbusDev.hpp"
#include <memory> // shared_ptr

class modbusTCP : public ADevice
{
public:
    modbusTCP();
    virtual ~modbusTCP();
    virtual int set(Json::Value value);
    virtual void addDev(int id,Json::Value value);
    virtual bool do_action(int parent,int device,int index,int value,int timeout);
    virtual bool do_action(int parent,int device,int index,int value,int timeout,bool return_last_state);
    virtual bool cancel_previous_timeout_job(int parent, int device, int index);
    virtual void do_recv_ex(int dio_type,int id,uint8_t *p_buf);
    virtual void do_soyal_recv_ex(int id, uint8_t *p_buf);
    void do_multiple_addr_recv_ex(int dio_type,int id,int addr, uint8_t *p_buf);

    void main_timeout();

protected:
    int connect_device();

    virtual bool do_recv();
    void do_state();
protected:
  std::vector<std::shared_ptr<modbusDev>>::iterator iterator;

  std::vector<std::shared_ptr<modbusDev>> devs;


private:
    virtual bool check_iter_valid();
    void addDevId(Json::Value it);
    void init_iter();

    bool do_soyal_recv();
    bool do_normal_recv();
    bool do_rs485_recv();
    bool do_liuchuan_elevator_recv();
    bool do_baochung_fire_fighting_recv();
    bool do_mitsubishi_elevator_recv();
    bool do_fuji_elevator_recv();
    bool do_yun_yang_fire_fighting_recv();
 private:


};

#endif
