#include <iostream>       // std::cout
#include <string>         // std::string
#include <json/json.h>

int main() {
    std::string raw = "{\"key\":\"value\",\"我\":\"是誰\",\"array\":[\"a\",\"b\",123]}";
    Json::Reader reader;
    Json::Value value;
    std::cout << "Input: " << raw << std::endl;
    if (reader.parse(raw, value)) {
        std::cout << "parsing: " << value ;//<< std::endl;
        std::cout << "get: " << value["我"] ;//<< std::endl;
        std::string data = value["key"].asString();//toStyledString();
        std::cout << "string: [" << data << "]" << std::endl;

        // with -std=c++11
        std::cout << "---" << std::endl;
        for (auto it : value) {
            std::cout << "elm: " << it ;//<< std::endl;
            if (it.isArray()) {
                for (auto it2 : it)
                    std::cout << "array data: " << it2 ;//<< std::endl;
            }
        }
    }
    return 0;
}
