
#ifndef __MYACCOUNT_HPP__
#define __MYACCOUNT_HPP__
#include <string>
#include <vector>
#include <json/json.h>
#include "wdef.h"

class myAccount
{
public:
   myAccount();
   virtual ~myAccount();
   int set(Json::Value value);
  protected:

protected:

public:
  std::string username;
  std::string passwd;
  std::string outbound;
  std::string domain;
  int port;
  std::string path;
  std::string elec_list;
  float elec_kw;

  int low;
  int mid;
  int high;

  int alarmdir_low;
  int alarm_low;

  int alarmdir_mid;
  int alarm_mid;

  int alarmdir_high;
  int alarm_high;

  int ftp_enable;
  std::string ftp_server;
  std::string ftp_user;
  std::string ftp_passwd;
  int ftp_port;

  std::string sms_username;
  std::string sms_passwd;

  int rec_sec;

  int del_alarm_enable;
  int  del_alarmdir;
  int  del_alarm;
  int limit_alarm_enable;
  int  limit_alarmdir;
  int  limit_alarm;

  int out_alarm_enable;
  int  out_alarmdir;
  int  out_alarm;

  int passwd_error_alarm_enable;
  int  passwd_error_alarmdir;
  int  passwd_error_alarm;

  int username_error_alarm_enable;
  int  username_error_alarmdir;
  int  username_error_alarm;        
};

#endif
