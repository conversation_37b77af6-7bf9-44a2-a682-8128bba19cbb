#include "waterMeterNode.hpp"
#include "baUtil.hpp"
#include "utility.hpp"
#include <math.h>
using namespace std;
bool waterMeterNode::to_hex(char *dest, size_t dest_len, const uint8_t *values, size_t val_len)
{
  if (dest_len < (val_len * 2 + 1)) /* check that dest is large enough */
    return false;
  *dest = '\0'; /* in case val_len==0 */
  while (val_len--)
  {
    /* sprintf directly to where dest points */
    sprintf(dest, "%02X", *values);
    dest += 2;
    ++values;
  }
  return true;
}
float waterMeterNode::getAccumulateWaterFlow(uint8_t *values)
{
  // uint8_t values[8] = {0x00, 0x11, 0x11, 0x31, 0x91, 0x20, 0x00, 0x06};
  char digitCountAfterDecimalPointChars[3];
  uint8_t digitCountAfterDecimalPointHex[1] = {values[7]};
  // cout << "digitCountAfterDecimalPointHex:" << +digitCountAfterDecimalPointHex[0];
  to_hex(digitCountAfterDecimalPointChars, sizeof(digitCountAfterDecimalPointChars), digitCountAfterDecimalPointHex, sizeof(digitCountAfterDecimalPointHex));
  int digitCountAfterDecimalPointInt = atoi(digitCountAfterDecimalPointChars);
  char buf[(sizeof(values) - 2) * 2 + 1]; /* one extra for \0 */

  stringstream ss;
  if (to_hex(buf, sizeof(buf), values, sizeof(values) - 2))
    ss << buf;

  char digitsAfterDecimalPointChars[digitCountAfterDecimalPointInt];
  char digitsBeforeDecimalPointChars[ss.str().length() - digitCountAfterDecimalPointInt];
  string digitsAfterDecimalPointString = ss.str().substr(ss.str().length() - digitCountAfterDecimalPointInt, digitCountAfterDecimalPointInt);
  string digitsBeforeDecimalPointString = ss.str().substr(0, ss.str().length() - digitCountAfterDecimalPointInt);
  strcpy(digitsAfterDecimalPointChars, digitsAfterDecimalPointString.c_str());
  strcpy(digitsBeforeDecimalPointChars, digitsBeforeDecimalPointString.c_str());
  stringstream combinedString;
  combinedString << digitsBeforeDecimalPointString << "." << digitsAfterDecimalPointString;
  float amountAfterDecimalPoint = stof(combinedString.str());
  return stof(digitsBeforeDecimalPointString) + stoi(digitsAfterDecimalPointString) / pow(10, 5);
}
waterMeterNode::waterMeterNode()
{
  oldAccumulateWaterFlow = -100;
  last_update_timestamp = 0;
  // m_temp = 0;
  // m_humidity = 0;
}

waterMeterNode::~waterMeterNode()
{
}

int waterMeterNode::set(Json::Value it)
{
  analogNode::set(it);

  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");

  uint8_t send[8];
  send[0] = m_addr;
  send[1] = 0x03;
  send[2] = 0x03;
  send[3] = 0x04;
  send[4] = 0;
  send[5] = 0x04;
  int crc;

  crc = crc_chk(send, 6);
  send[6] = crc % 256;
  send[7] = crc / 256;

  RS485Msg msg;
  msg.setData(send, 8);
  m_msg.push_back(move(msg));

  iterator = m_msg.begin();

  return 1;
}

bool waterMeterNode::set_data(uint8_t *p_data, int len)
{
  bool ret;

  ret = false;
  if (p_data[0] == m_addr)
  {
    // int temp = p_data[3]*256+p_data[4];
    // int humidity = p_data[5]*256+p_data[6];
    uint8_t reversedValues[len - 4];
    // for (int i = 0; i < len; i++) {
    //   cout << "water" << +p_data[i] << " ";
    // }
    // cout<< endl <<"waterlength" << len << " " << sizeof(p_data);
    // cout<<+p_data;
    for (int i = 3; i < len - 2; i++)
    {
      // reversedValues[i-3] = p_data[sizeof(p_data) - i];
      reversedValues[i - 3] = p_data[i];
      stringstream ss;
      ss << reversedValues[i - 3];
      // cout << endl <<"waterrr" << +reversedValues[i-3];
    }
    try
    {
      float newAccumulateWaterFlow = getAccumulateWaterFlow(reversedValues);
      // if (newAccumulateWaterFlow != oldAccumulateWaterFlow)
      // {
      // }
      oldAccumulateWaterFlow = newAccumulateWaterFlow;
      updateMessage();
    }
    catch (const std::exception &e)
    {
      std::cerr << e.what() << '\n';
      cout << "water meter error : " << m_id << endl;
    }

    // if(temp != m_temp || humidity != m_humidity)
    // {
    //   m_temp = temp;
    //   m_humidity = humidity;
    //   updateMessage();
    // }

    ret = true;

    //iterator++;
  }
  return ret;
}

void waterMeterNode::updateMessage()
{
  int current_timestamp = static_cast<int>(time(NULL));
  if (current_timestamp > (last_update_timestamp + 1*60))
  {
    last_update_timestamp = current_timestamp;
    static int s_cnt = MAX_UPDATEMESSAGE;
    bool is_show;

    is_show = false;
    if (++s_cnt > MAX_UPDATEMESSAGE)
    {
      s_cnt = 0;
      is_show = true;
    }
    stringstream ss;
    ss << "/index.php?option=\"com_floor&task=sroots.update_watermeter&accumulateWaterFlow=" << oldAccumulateWaterFlow << "&id=" << m_id << "\" ";
    // cout << ss.str() << endl;
    WSendMsg msg;

    msg.set(ss.str(), "/tmp/sendrs485watermetermsg", "/tmp/SENDRS485WATERMETERMSG", true, false);
    baUtil::add_sendMessage(&msg);
  }
}
