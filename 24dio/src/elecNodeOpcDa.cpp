#include <ctime>
#include "elecNodeOpcDa.hpp"

#include "baUtil.hpp"
#include "utility.hpp"

using namespace std;
#define MAX_RS485_DATA 20
elecNodeOpcDa::elecNodeOpcDa()
{
  kwh = 0;
  kw = 0;
  volage = 0;
  current = 0;
  A_A = 0;
  A_B = 0;
  A_C = 0;
  V_BN = 0;
  V_CN = 0;
  kvar = 0;
    
  freq = 0;
  power_factor = 0;
  update_kwh_timestamp = 0;
  update_timestamp = 0;
  old_kwh = 0;
}

elecNodeOpcDa::~elecNodeOpcDa()
{
   cout << "opcda elec1" << endl;
}

int elecNodeOpcDa::set(Json::Value it)
{
  // cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;
  analogNode::set(it);
  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");

  int reg_arr[] =
      {
          10, // 0@A0 00, KWH 4 0.01kwh ulong
              // 321,   //1@01 41 psum 2 (kw total?) 0.01kw int 1
              // 305,   //2@01 31 V1 (V A-N?) 0.01v uint 2
              // 305,    //3@01 31 I Avg (巧力沒有)
              // 304,   //4@01 30@2 F (HZ) 0.01h uint 1
              // 333,   //5@01 4d PF 0.001 int 1
              // 307,  //6@01 33 V2 (V BN?) 0.01v uint 2
              // 309,   //7@01 35 V3 (V CN?) 0.01v uint 2
              // 312,   //8@01 38 I1 (A A?) 0.01a uint 2
              // 314,   //9@01 3a I2 (A B?) 0.01a uint 2
              // 316,   //10@01 3c I3 (A C?) 0.01a uint 2
              // 325    //11@01 45 qsum (kvar?) 0.01kvar int 1
      };
  uint8_t send[MAX_RS485_DATA];
  uint16_t crc;
  cout << "opcda elec" << endl;
  for (int i = 0; i < sizeof(reg_arr) / sizeof(reg_arr[0]); i++)
  {

    send[0] = send[1] = send[2] = send[3] = send[4] = 0x00;
    send[5] = 0x06;
    send[6] = m_addr;
    //01 03 00 02 00 0A 64 0D\

    send[7] = 0x03;
    send[8] = 0x00;
    send[9] = 0x02;
    send[10] = (reg_arr[i] + 0) / 256;
    send[11] = (reg_arr[i] + 0) % 256;
    send[12] = 0x64;
    send[13] = 0x0d;
    // send[1] = 0x03;
    // send[2] = (reg_arr[i] +0)/ 256;
    // send[3] = (reg_arr[i] +0)% 256;
    // send[4] = 0;
    // // send[5] = 0x02;
    // if (i == 0 || i == 2 || i == 6 || i ==7 || i == 8 || i == 9 || i == 10)
    //   send[5] = 0x02;
    // else
    //   send[5] = 0x01;

    // crc = crc_chk(send, 6);
    // send[6] = crc % 256;
    // send[7] = crc / 256;

    RS485Msg msg;
    msg.setData(send, 14);
    m_msg.push_back(msg);
  }

  iterator = m_msg.begin();

  baUtil::addElecNodes(this);

  return 1;
}

bool elecNodeOpcDa::set_data(uint8_t *p_data, int len)
{
  // if (p_data[0] != m_addr)
  // {
  //   return false;
  // }
  // int aspect_length = 2;
  // if (m_index == 0)
  // {
  //   aspect_length = 4;
  // }
  // aspect_length += 3;
  // uint16_t crc = crc_chk(p_data, aspect_length);
  // if (p_data[1] == 0x83 || p_data[1] != 0x03)
  // {
  //   for (size_t i = 0; i < len; i++)
  //   {
  //     printf("%02X ", p_data[i]);
  //     /* code */
  //   }

  //   cout << "cic response 83 error" << endl;
  //   return true;
  // }
  // if (crc % 256 != p_data[aspect_length] || crc / 256 != p_data[aspect_length + 1])
  // {
  //   for (size_t i = 0; i < len; i++)
  //   {
  //     printf("%02X ", p_data[i]);
  //     /* code */
  //   }

  //   cout << "cic crc checksum error " << aspect_length << ", m_index:" << m_index << endl;
  //   return true;
  // }
  // cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<m_index<<endl;
  // usleep(10000);
  //  float val;
  //  float *p_val;
  //  int* int_val_1;
  unsigned int int_val;
  uint8_t buf4[4];
  // uint8_t buf2[2];

  // uint8_t buf[2];

  // buf[0] = p_data[3];
  // buf[1] = p_data[4];

  // cout << "m_index:" << m_index << ",";
  // for (size_t i = 0; i < 4; i++)
  // {
  //   cout << static_cast<int>(p_data[6 - i]) << ",";
  // }
  // cout << endl
  //      << endl;

  // int_val = *(int *)&buf4[0];
  float float_value;
    // float float_value = *(float *)&buf[0];
  switch (m_index)
  {
  case 0:
    buf4[0] = p_data[12];
    buf4[1] = p_data[11];
    buf4[2] = p_data[10];
    buf4[3] = p_data[9];
    float_value = *(float *)&buf4[0];
    kw = (int)(float_value*100);
    buf4[0] = p_data[16];
    buf4[1] = p_data[15];
    buf4[2] = p_data[14];
    buf4[3] = p_data[13];
    float_value = *(float *)&buf4[0];
    kwh = (long)(100 * float_value);
    buf4[0] = p_data[20];
    buf4[1] = p_data[19];
    buf4[2] = p_data[18];
    buf4[3] = p_data[17];
    float_value = *(float *)&buf4[0];
    A_A = (int)(100 * float_value);
    buf4[0] = p_data[24];
    buf4[1] = p_data[23];
    buf4[2] = p_data[22];
    buf4[3] = p_data[21];
    float_value = *(float *)&buf4[0];
    volage = (int)(100 * float_value);
    updateMessage();
    break;
    // case 1:
    //   buf4[0] = p_data[4];
    //   buf4[1] = p_data[3];
    //   kw = *(int *)&buf4[0];
    //   break;
    // case 2:
    //   buf4[0] = p_data[6];
    //   buf4[1] = p_data[5];
    //   buf4[2] = p_data[4];
    //   buf4[3] = p_data[3];
    //   volage = (int)*(long *)&buf4[0];
    //   break;
    // case 3:
    //   // current = (int)int_val;
    //   break;
    // case 4:
    //   buf4[0] = p_data[4];
    //   buf4[1] = p_data[3];
    //   freq = *(int *)&buf4[0];
    //   break;
    // case 5:
    //   buf4[0] = p_data[4];
    //   buf4[1] = p_data[3];
    //   power_factor = *(int *)&buf4[0]/10;

    //   break;
    // case 6:
    //   buf4[0] = p_data[6];
    //   buf4[1] = p_data[5];
    //   buf4[2] = p_data[4];
    //   buf4[3] = p_data[3];
    //   V_BN = (int)*(long *)&buf4[0];
    //   break;
    // case 7:
    //   buf4[0] = p_data[6];
    //   buf4[1] = p_data[5];
    //   buf4[2] = p_data[4];
    //   buf4[3] = p_data[3];
    //   V_CN = (int)*(long *)&buf4[0];
    //   break;
    // case 8:
    //   buf4[0] = p_data[6];
    //   buf4[1] = p_data[5];
    //   buf4[2] = p_data[4];
    //   buf4[3] = p_data[3];
    //   A_A = (int)*(long *)&buf4[0];
    //   break;
    // case 9:
    //   buf4[0] = p_data[6];
    //   buf4[1] = p_data[5];
    //   buf4[2] = p_data[4];
    //   buf4[3] = p_data[3];
    //   A_B = (int)*(long *)&buf4[0];
    //   break;
    // case 10:
    //   buf4[0] = p_data[6];
    //   buf4[1] = p_data[5];
    //   buf4[2] = p_data[4];
    //   buf4[3] = p_data[3];
    //   A_C = (int)*(long *)&buf4[0];

    //   break;
    // case 11:
    //   buf4[0] = p_data[4];
    //   buf4[1] = p_data[3];
    //   kvar = *(int *)&buf4[0];
    //   updateMessage();
    //   break;
  }

  m_index++;

  if (m_index >= m_msg.size())
  {
    m_index = 0;
  }

  return true;
}