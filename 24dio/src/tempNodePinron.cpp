#include "tempNodePinron.hpp"
#include "baUtil.hpp"
#include "utility.hpp"
using namespace std;

tempNodePinron::tempNodePinron()
{
  m_temp = 0;
  m_humidity = 0;
  humidity_weight = 0.1;
  temperature_weight = 0.1;
  m_co2_ppm_alarm_triggered = m_humidity_alarm_triggered = m_temperature_alarm_triggered = false;
  status = 1;
}

tempNodePinron::~tempNodePinron()
{
}

int tempNodePinron::set(Json::Value it)
{
  analogNode::set(it);

  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");
  set_int_value(it, &status, "status");

  set_int_value(it, &m_humidity_alarmdir, "humidity_alarmdir");
  set_int_value(it, &m_humidity_alarm, "humidity_alarm");
  set_float_value(it, &m_humidity_alarm_threshold, "humidity_alarm_threshold");

  set_int_value(it, &m_temperature_alarmdir, "temperature_alarmdir");
  set_int_value(it, &m_temperature_alarm, "temperature_alarm");
  set_float_value(it, &m_temperature_alarm_threshold, "temperature_alarm_threshold");

  set_int_value(it, &m_co2_ppm_alarmdir, "co2_ppm_alarmdir");
  set_int_value(it, &m_co2_ppm_alarm, "co2_ppm_alarm");
  set_int_value(it, &m_co2_ppm_alarm_threshold, "co2_ppm_alarm_threshold");

  set_int_value(it, &m_co2_ppm, "co2_ppm");
  set_float_value(it, &m_humidity, "humidity");
  set_float_value(it, &m_temp, "temp");

  if (m_co2_ppm > m_co2_ppm_alarm_threshold)
  {
    m_co2_ppm_alarm_triggered = true;
  }
  if (m_humidity > m_humidity_alarm_threshold)
  {
    m_humidity_alarm_triggered = true;
  }
  if (m_temp > m_temperature_alarm_threshold)
  {
    m_temperature_alarm_triggered = true;
  }

  triggerAlarm();

  uint8_t send[12];
  send[0] = m_addr;
  send[1] = send[2] = send[3] = send[4] = 0x00;
  send[5] = 0x06;

  // uint16_t addr = 
  send[6] = 0x01;
  send[7] = 0x03;
  send[8] = ((m_addr * 10)-1) / 256;
  send[9] = ((m_addr * 10)-1) % 256;
  send[10] = 0;
  send[11] = 0x0a;
  // int crc;

  // crc = crc_chk(send, 6);
  // send[6] = crc % 256;
  // send[7] = crc / 256;

  RS485Msg msg;
  msg.setData(send, 12);
  m_msg.push_back(std::move(msg));

  iterator = m_msg.begin();

  return 1;
}
void tempNodePinron::triggerAlarm()
{

  // if (m_humidity >= m_humidity_alarm_threshold && !m_humidity_alarm_triggered)
  // {
  //   m_humidity_alarm_triggered = true;
  //   status = 3;
  //   // status = 3;
  //   // baUtil::do_alarm(dio_alarmdir_id, dio_alarm_id);
  //   do_di_event(m_humidity_alarmdir, m_humidity_alarm);
  //   // cout << "coSensor alarm" << endl;
  // }
  // else if (m_humidity < (0.95 * m_humidity_alarm_threshold) && m_humidity_alarm_triggered)
  // {
  //   m_humidity_alarm_triggered = false;
  //   if (!(m_temperature_alarm_triggered && m_co2_ppm_alarm_triggered))
  //   {
  //     status = 1;
  //   }
  //   // status = 1;
  //   // baUtil::clear_alarm(dio_alarmdir_id, dio_alarm_id);
  //   do_di_release(m_humidity_alarmdir, m_humidity_alarm);
  //   // cout << "coSensor clear alarm" << endl;
  // }

  if (m_temp >= m_temperature_alarm_threshold && !m_temperature_alarm_triggered)
  {
    m_temperature_alarm_triggered = true;
    status = 3;
    // status = 3;
    // baUtil::do_alarm(dio_alarmdir_id, dio_alarm_id);
    do_di_event(m_temperature_alarmdir, m_temperature_alarm);
    // cout << "coSensor alarm" << endl;
  }
  else if (m_temp < (0.95 * m_temperature_alarm_threshold) && m_temperature_alarm_triggered)
  {
    m_temperature_alarm_triggered = false;
    if (!(m_humidity_alarm_triggered && m_co2_ppm_alarm_triggered))
    {
      status = 1;
    }
    // status = 1;
    // baUtil::clear_alarm(dio_alarmdir_id, dio_alarm_id);
    do_di_release(m_temperature_alarmdir, m_temperature_alarm);
    // cout << "coSensor clear alarm" << endl;
  }

  if (m_co2_ppm >= m_co2_ppm_alarm_threshold && !m_co2_ppm_alarm_triggered)
  {
    m_co2_ppm_alarm_triggered = true;
    status = 3;
    // status = 3;
    // baUtil::do_alarm(dio_alarmdir_id, dio_alarm_id);
    do_di_event(m_co2_ppm_alarmdir, m_co2_ppm_alarm);
    // cout << "coSensor alarm" << endl;
  }
  else if (m_co2_ppm < (0.95 * m_co2_ppm_alarm_threshold) && m_co2_ppm_alarm_triggered)
  {
    m_co2_ppm_alarm_triggered = false;
    if (!(m_humidity_alarm_triggered && m_temperature_alarm_triggered))
    {
      status = 1;
    }
    // status = 1;
    // baUtil::clear_alarm(dio_alarmdir_id, dio_alarm_id);
    do_di_release(m_co2_ppm_alarmdir, m_co2_ppm_alarm);
    // cout << "coSensor clear alarm" << endl;
  }
  // if (get_di_alarm_triggered())
  // {
  //   status = 3;
  // }
  // else
  // {
  //   status = 1;
  // }
}
bool tempNodePinron::get_di_alarm_triggered()
{
  return m_humidity_alarm_triggered || m_temperature_alarm_triggered || m_co2_ppm_alarm_triggered;
}
DIOState tempNodePinron::get_ediostate()
{
  if (status == 3)
  {
    return DIOState::DIO_ALARM;
  }
  else
  {
    return DIOState::DIO_IDLE;
  }
}
void tempNodePinron::do_di_event(int alarmdir, int alarm)
{
  AEvent event;

  // event.name = m_name;
  // event.state = m_state;
  event.alarmdir = alarmdir;
  event.alarm = alarm;
  event.id = m_id;
  event.pid = 1;
  event.index = 0;
  event.status = get_ediostate();

  baUtil::do_di_event(&event);
}

void tempNodePinron::do_di_release(int alarmdir, int alarm)
{
  AEvent event;

  // event.name = m_name;
  // event.state = m_state;
  event.alarmdir = alarmdir;
  event.alarm = alarm;
  event.id = m_id;
  event.pid = 1;
  event.index = 0;
  event.status = get_ediostate();

  baUtil::do_di_release(&event);
}
bool tempNodePinron::set_data(uint8_t *p_data, int len)
{
  bool ret;

  ret = false;

  if (p_data[0] == m_addr)
  {
    uint8_t buf[4];

    buf[0] = p_data[12];
    buf[1] = p_data[11];
    buf[2] = p_data[10];
    buf[3] = p_data[9];
    // p_val = (float *)&buf[0];
    
    m_temp =  *(float *)&buf[0] * temperature_weight ;
    // cout << "m_temp: " << len << ", " <<unsigned(p_data[12])<< "," << unsigned(p_data[11]) << "," << unsigned(p_data[10]) << "," << unsigned(p_data[9] )<< endl;

    buf[0] = p_data[14];
    buf[1] = p_data[13];
    // p_val = (int16_t *)&buf[0];
    // float humidity = (p_data[5] * 256 + p_data[6]) * humidity_weight;
    m_co2_ppm = *(int16_t *)&buf[0];
    updateMessage();

    // if (temp != m_temp || humidity != m_humidity)
    {
      // m_temp = temp;
      // // m_humidity = humidity;
      // m_co2_ppm = co2_ppm;
    }

    ret = true;

    // iterator++;
  }

  return ret;
}

void tempNodePinron::updateMessage()
{
  int current_timestamp = static_cast<int>(time(NULL));
  if (current_timestamp > (last_update_timestamp + 30))
  {
    last_update_timestamp = current_timestamp;
    triggerAlarm();
    sendMessageToBa();
  }
}
void tempNodePinron::sendMessageToBa()
{
  static int s_cnt = MAX_UPDATEMESSAGE;
  bool is_show;

  is_show = false;
  if (++s_cnt > MAX_UPDATEMESSAGE)
  {
    s_cnt = 0;
    is_show = true;
  }
  std::stringstream ss;
  ss << "/index.php?option=\"com_floor&task=sroots.update_temp2&co2_ppm=" << m_co2_ppm << "&temp=" << m_temp << "&humidity=" << m_humidity << "&id=" << m_id << "\" ";

  WSendMsg msg;

  msg.set(ss.str(), "/tmp/sendrs485tempmsg", "/tmp/SENDRS485TEMPMSG", true, is_show);
  baUtil::add_sendMessage(&msg);
}
