#include "ipNode.hpp"

#include "utility.hpp"
using namespace std;

ipNode::ipNode()
{
    online_cnt = 0;
    offline_cnt = 0;

    state = EDIOState::RELEASE;
}

ipNode::~ipNode()
{
    //cout<<"\ndioNode::"<<__func__<<endl;
}

int ipNode::set(Json::Value it)
{
  set_int_value(it,&id,"id");
  set_string_value(it,&info,"info");
  set_string_value(it,&note,"note");

  int int_status;
  set_int_value(it,&int_status,"status");

  //cout <<"\n"<<id<<" "<<info<<" "<<int_status<<endl;
  if(int_status == 3)
  {
    state = EDIOState::ALARM;
  }

  return 1;
}

void ipNode::writeToFile(EDIOState use_status, ofstream& file)
{
    bool need_write;

    //cout<<"\nipNode::"<<__func__<<" "<<online_cnt<<endl;
    need_write = false;
    if(use_status == state)
    {
        if(use_status == EDIOState::RELEASE && online_cnt == 0)
        {
          online_cnt = online;
          need_write = true;
        }
        else if(use_status == EDIOState::ALARM && offline_cnt == 0)
        {
          offline_cnt = offline;
          need_write = true;
          //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<info<<endl;
        }

        if(need_write)
        {
            //cout<<"\nipNode1::"<<__func__<<endl;
            file <<info<<"\n";
        }
    }
}

bool ipNode::update(string str,EDIOState now_status,map_int_string& ids_map)
{
    bool ret;

    ret = false;
    if(str == info)
    {
      online_cnt = online;
      offline_cnt = offline;

      state = now_status;

      ids_map[id] = note;

      ret = true;
    }

    return ret;
}

void ipNode::main_timeout()
{
  if(online_cnt > 0)
      online_cnt--;

  if(offline_cnt > 0)
      offline_cnt--;

  if(online_cnt == 0)
  {
    //cout<<"\nipNode::"<<__func__<<endl;
  }
}
