#ifndef __WATERMETERNODETKD_HPP__
#define __WATERMETERNODETKD_HPP__
#include <json/json.h>

#include "analogNode.hpp"
class waterMeterNodeTkd : public analogNode
{
public:
  waterMeterNodeTkd();
  virtual ~waterMeterNodeTkd();

  int set(Json::Value value);
  bool set_data(uint8_t *p_data, int len);
  int getIndex();
  int getPid();

protected:
  void updateMessage();

protected:
  int index;
  int m_id;
  int m_dio_type;
  int update_timestamp;
  double accumulateWaterFlow;
  int decimal_digits;
};

#endif
