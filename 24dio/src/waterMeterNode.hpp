#ifndef __WATERMETERNODE_HPP__
#define __WATERMETERNODE_HPP__
#include <json/json.h>

#include "analogNode.hpp"
class waterMeterNode : public analogNode
{
public:
  bool to_hex(char *dest, size_t dest_len, const uint8_t *values, size_t val_len);
  float getAccumulateWaterFlow(uint8_t *values);
  waterMeterNode();
  virtual ~waterMeterNode();

  int set(Json::Value value);
  bool set_data(uint8_t *p_data, int len);

private:
  void updateMessage();

private:
  int m_id;
  int m_dio_type;
  // int m_temp;
  // int m_humidity;
  int last_update_timestamp;
  float oldAccumulateWaterFlow;
  uint8_t m_buf[13];
};

#endif
