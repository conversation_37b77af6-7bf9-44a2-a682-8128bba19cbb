#include <ctime>
#include "elecNodeBenderPem333.hpp"

#include "baUtil.hpp"
#include "utility.hpp"

using namespace std;
#define MAX_RS485_DATA 10
elecNodeBenderPem333::elecNodeBenderPem333()
{
  kwh = 0;
  kw = 0;
  volage = 0;
  current = 0;
  freq = 0;
  power_factor = 0;

  update_kwh_timestamp = 0;
  update_timestamp = 0;
  old_kwh = 0;
}

elecNodeBenderPem333::~elecNodeBenderPem333()
{
}

int elecNodeBenderPem333::set(Json::Value it)
{
  //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;
  analogNode::set(it);
  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");

  int reg_arr[] =
      {
          100,  //KWH
          30, // kw total int32 /1000kw
          8,  // V1 (V A-N?) uint32 /100v
          22, // I Avg uint32 /1000a
          52, // F (HZ) uint16 /1000
          51, // PF int16 /1000
          10, // V2 (V BN?)uint32 /100v
          12, // V3 (V CN?)uint32 /100v
          16, // I1 (A A?) uint32 /1000a
          18, // I2 (A B?) uint32 /1000a
          20, // I3 (A C?) uint32 /1000a
          38  // qsum (kvar?) int32 / 1000kvar
      };
  uint8_t send[MAX_RS485_DATA];
  uint16_t crc;

  for (int i = 0; i < sizeof(reg_arr) / sizeof(reg_arr[0]); i++)
  {
    send[0] = m_addr;
    send[1] = 0x03;
    send[2] = reg_arr[i] / 256;
    send[3] = reg_arr[i] % 256;
    send[4] = 0;
    if (i == 0)
      send[5] = 0x04;
    else
      send[5] = 0x02;

    crc = crc_chk(send, 6);
    send[6] = crc % 256;
    send[7] = crc / 256;

    RS485Msg msg;
    msg.setData(send, 8);
    m_msg.push_back(msg);
  }

  iterator = m_msg.begin();

  baUtil::addElecNodes(this);

  return 1;
}

bool elecNodeBenderPem333::set_data(uint8_t *p_data, int len)
{
  if (p_data[0] != m_addr)
  {
    return false;
  }

  //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<m_index<<endl;
  //usleep(10000);
  // float val;
  // float *p_val;
  // int* int_val_1;
  unsigned int int_val;
  uint8_t buf[4];

  // int reg_arr[] =
  //     {
  //         0, //KWH
  //         30,   // kw total int32 /1000kw
  //         0,   // V1 (V A-N?) uint32 /100v
  //         22,     // I Avg uint32 /1000a
  //         52,   // F (HZ) int16 /1000
  //         51,   // PF int16 /1000
  //         2,  // V2 (V BN?)uint32 /100v
  //         4,   // V3 (V CN?)uint32 /100v
  //         16,   // I1 (A A?) uint32 /1000a
  //         18,   // I2 (A B?) uint32 /1000a
  //         20,   // I3 (A C?) uint32 /1000a
  //         38    // qsum (kvar?) int32 / 1000kvar
  //     };
  buf[0] = p_data[6];
  buf[1] = p_data[5];
  buf[2] = p_data[4];
  buf[3] = p_data[3];
  // cout << "m_index:" << m_index << ",";
  // for (size_t i = 0; i < 4; i++)
  // {
  //   cout << static_cast<int>(p_data[6 - i]) << ",";
  // }
  // cout << endl
  //      << endl;
  unsigned long t = 0;
  long lt = 0;
  short int short_i_v = 0;
  int i = 0;
  int_val = *(unsigned int *)&buf[0];
  switch (m_index)
  {
  case 0:
    // uint8_t buf0[8];
    // buf0[0] = p_data[10];
    // buf0[1] = p_data[9];
    // buf0[2] = p_data[8];
    // buf0[3] = p_data[7];
    // buf0[4] = p_data[6];
    // buf0[5] = p_data[5];
    // buf0[6] = p_data[4];
    // buf0[7] = p_data[3];
    kwh = (long)(*(unsigned int *)&buf[0]) / 0.1;
    break;
  case 1:
    lt = *(long *)&buf[0];
    kw = (int)lt / 10;
    break;
  case 2:

    t = *(unsigned long *)&buf[0];
    volage = (int)t;
    break;
  case 3:
    current = (int)int_val / 10;
    break;
  case 4:
    short_i_v = *(int *)&buf[2];
    freq = (int)short_i_v;
    // freq = buf[0] * 256 + buf[1];
    break;
  case 5:
    short_i_v = *(int *)&buf[2];
    power_factor = (int)short_i_v / 10;
    // power_factor = buf[0] * 256 + buf[1];

    break;
  case 6:
    t = *(unsigned long *)&buf[0];
    V_BN = (int)t;
    break;
  case 7:
    t = *(unsigned long *)&buf[0];
    V_CN = (int)t;
    break;
  case 8:
    t = *(unsigned long *)&buf[0];
    A_A = (int)t / 10;
    break;
  case 9:
    t = *(unsigned long *)&buf[0];
    A_B = (int)t / 10;
    break;
  case 10:
    t = *(unsigned long *)&buf[0];
    A_C = (int)t / 10;

    break;
  case 11:
    i = *(int *)&buf[0];
    kvar = i*100;
    updateMessage();
    break;
  }

  m_index++;

  if (m_index >= m_msg.size())
  {
    m_index = 0;
  }

  return true;
}