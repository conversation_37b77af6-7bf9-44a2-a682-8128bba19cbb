#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <string.h>
#include <netdb.h>
#include <sys/types.h>
#include <netinet/in.h>
#include <sys/socket.h>
#include <unistd.h>
#include <time.h>

#include <arpa/inet.h>
#include <sys/types.h>
#include <sys/un.h>
#include <signal.h>
#include <sys/ioctl.h>
#include <pthread.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <string>
#include <vector>
#include <sstream>
#include <fstream>
#include <iostream>
#include <fcntl.h>
#include <ctime>
#include <map>
#include "crc.h"

using namespace std;

#define DIO_NUMBER 24
#define MAXDATASIZE 100
#define MAX_DIO_TIMEOUT 3
#define MAX_DIO_BUF MAXDATASIZE
#define MAX_CONNECT_TIMEOUT 120
#define PASS_HOUR 3600

static int m_maxfd;

static map<int, string> mapRecv;

static char m_server_ipaddr[]="127.0.0.1";
#define MAX(x, y) (((x) > (y)) ? (x) : (y))
#define MIN(x, y) (((x) < (y)) ? (x) : (y))

static int connect_device();

int connect_device()
{
     int sockfd;

     string ip_addr = "************";
     int port = 1631;

     struct sockaddr_in their_addr; /* connector's address information */
     if ((sockfd = socket(AF_INET, SOCK_STREAM, 0)) <= 0) {
         perror("socket");

         return 0;
     }

     their_addr.sin_family = AF_INET;      /* host byte order */
     their_addr.sin_port = htons(port);    /* short, network byte order */

     their_addr.sin_addr.s_addr = inet_addr(ip_addr.c_str());//*((struct in_addr *)he->h_addr);
     bzero(&(their_addr.sin_zero), 8);     /* zero the rest of the struct */
#define TCP_SYNCNT 7
     //int synRetries = 2; // Send a total of 3 SYN packets => Timeout ~7s
     //setsockopt(sockfd, IPPROTO_TCP, TCP_SYNCNT, &synRetries, sizeof(synRetries));
  int soc;
  int res;
  struct sockaddr_in addr;
  long arg;
  fd_set myset;
  struct timeval tv;
  int valopt;
  socklen_t lon;

  addr = their_addr;
  // Set non-blocking
  soc = sockfd;

  if( (arg = fcntl(soc, F_GETFL, NULL)) < 0) {
     fprintf(stderr, "Error fcntl(..., F_GETFL) (%s)\n", strerror(errno));
     exit(0);
  }
  arg |= O_NONBLOCK;
  if( fcntl(soc, F_SETFL, arg) < 0) {
     fprintf(stderr, "Error fcntl(..., F_SETFL) (%s)\n", strerror(errno));
     exit(0);
  }
  // Trying to connect with timeout
  res = connect(soc, (struct sockaddr *)&addr, sizeof(addr));
  if (res < 0) {
     if (errno == EINPROGRESS) {
        fprintf(stderr, "EINPROGRESS in connect() - selecting\n");
        do {
           tv.tv_sec = 2;
           tv.tv_usec = 0;
           FD_ZERO(&myset);
           FD_SET(soc, &myset);
           res = select(soc+1, NULL, &myset, NULL, &tv);
           if (res < 0 && errno != EINTR) {
              fprintf(stderr, "Error connecting %d - %s\n", errno, strerror(errno));
              exit(0);
           }
           else if (res > 0) {
              // Socket selected for write
              lon = sizeof(int);
              if (getsockopt(soc, SOL_SOCKET, SO_ERROR, (void*)(&valopt), &lon) < 0) {
                 fprintf(stderr, "Error in getsockopt() %d - %s\n", errno, strerror(errno));
                 exit(0);
              }
              // Check the value returned...
              if (valopt) {
                 fprintf(stderr, "Error in delayed connection() %d - %s\n", valopt, strerror(valopt)
);
                 exit(0);
              }
              break;
           }
           else {
              fprintf(stderr, "Timeout in select() - Cancelling!\n");
              exit(0);
           }
        } while (1);
     }
     else {
        fprintf(stderr, "Error connecting %d - %s\n", errno, strerror(errno));
        exit(0);
     }
  }
  // Set to blocking mode again...
  if( (arg = fcntl(soc, F_GETFL, NULL)) < 0) {
     fprintf(stderr, "Error fcntl(..., F_GETFL) (%s)\n", strerror(errno));
     exit(0);
  }
  arg &= (~O_NONBLOCK);
  if( fcntl(soc, F_SETFL, arg) < 0) {
     fprintf(stderr, "Error fcntl(..., F_SETFL) (%s)\n", strerror(errno));
     exit(0);
  }

     //fcntl(sockfd, F_SETFL, O_NONBLOCK);

     printf("1111");
     fflush(stdout);
/*
     if (connect(sockfd, (struct sockaddr *)&their_addr, \
                                        sizeof(struct sockaddr)) == -1) {
         //cout << getNowTime();
         printf("connect123");
         fflush(stdout);

         //exit(1);
         return 0;
     }
*/
     printf("1212");
     fflush(stdout);
     int fd;
     fd = sockfd;

     if(fd > 0)
     {
         //ss << "@SET TIME "<<tm.tm_hour<<" "<<tm.tm_min<<" "<<tm.tm_sec<<" \r";
         unsigned char buf[100];
         int mylen;

         int reg;
         int crc;
         reg = 3191;
         mylen = 8;
         buf[0] = 2;
         buf[1] = 0x03;
         buf[2] = reg/256;
         buf[3] = reg%256;
         buf[4] = 0;
         buf[5] = 0x02;

         crc = crc_chk(buf,6);
         buf[6]=crc%256;
         buf[7]=crc/256;

         buf[0] = 0x7E;
         buf[1] = 0x05;
         buf[2] = 0x01;//DO_addr_start;
         buf[3] = 0x21;
         buf[4] = 0x00;

         buf[5] = buf[2]^0x21^0x00^0xFF;
         buf[6] = buf[2]+buf[4]+0x21+buf[5];

         mylen = 7;

         if (send(fd, buf,mylen , 0) == -1){
               perror("send");
               //exit (1);

         }
         else
         {
            cout<<"send ok"<<endl;
            fflush(stdout);
         }
         while(1)
         {
             char data[100];
             int nread;
             ioctl(fd,FIONREAD,&nread);
             if(nread > 0)
             {
               int res = read(fd, data, nread);
               int i;
               printf("\ndata ");
               for(i=0;i<res;i++)
               {
                 printf("0x%02X, ",(unsigned char)data[i]);
               }
               printf("\n");
               fflush(stdout);
               break;
             }
         }
         return 0;
         sleep(1);
         #if 0
         buf[0] = 0x01;//0x02;
         buf[1] = 0x03;
         buf[2] = 0;//3035/256;
         buf[3] = 0;//3035%256;
         buf[4] = 0x00;
         buf[5] = 0x02;
         #else
         buf[0] = 0x7E;
         buf[1] = 0x04;
         buf[2] = 0x01;
         buf[3] = 0x18;
         buf[4] = 0xE6;
         buf[5] = 0xFF;
         #endif
         crc = crc_chk((unsigned char*)buf,6);
         buf[6]=crc%256;
         buf[7]=crc/256;

         //buf[7] = 0x0B;
         //buf[6] = (unsigned char)0xC4;
         mylen = 6;
         //#endif
         if (send(fd, buf,mylen , 0) == -1){
               perror("send");
               //exit (1);

         }
         else
         {
            cout<<"send ok"<<endl;
            fflush(stdout);
         }
     }

     while(1)
     {
         char data[100];
         int nread;
         ioctl(fd,FIONREAD,&nread);
         if(nread > 0)
         {
           int res = read(fd, data, nread);
           int i;
           printf("\ndata ");
           for(i=0;i<res;i++)
           {
             printf("0x%02X, ",(unsigned char)data[i]);
           }
           printf("\n");
           fflush(stdout);
           break;
         }
     }
     close(sockfd);
     return sockfd;

}


#define SOCKET_NAME "/tmp/diosocket"



int main(int argc, char *argv[])
{
    //int sockfd, numbytes;
    //char buf[MAXDATASIZE];
    //struct hostent *he;
    argv[0] = argv[0];
    argc = argc;

    connect_device();
    return 0;

}
