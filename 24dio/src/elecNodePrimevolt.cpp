#include <ctime>
#include "elecNodePrimevolt.hpp"

#include "baUtil.hpp"
#include "utility.hpp"

using namespace std;
#define MAX_RS485_DATA 10
elecNodePrimevolt::elecNodePrimevolt()
{
  kwh = 0;
  kw = 0;
  volage = 0;
  current = 0;
  freq = 0;
  power_factor = 0;

  update_kwh_timestamp = 0;
  update_timestamp = 0;

  old_kwh = 0;
}

elecNodePrimevolt::~elecNodePrimevolt()
{
}

int elecNodePrimevolt::set(Json::Value it)
{
  //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;
  analogNode::set(it);
  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");

  int reg_arr[] =
      {
          4129, //KWH kwh 
          // 1058,   // psum 2 (kw total?) kw float
          // 1032,   // V1 (V A-N?) v float
          // 1046,     // I Avg (巧力沒有)
          // 1050,   // F (HZ) hz float
          // 1082,   // PF  float
          // 1034,  // V2 (V BN?) v float
          // 1036,   // V3 (V CN?) v float
          // 1040,   // I1 (A A?) a float
          // 1042,   // I2 (A B?) a float
          // 1044,   // I3 (A C?) a float
          // 1066    // qsum (kvar?) kvar float
      };
  uint8_t send[MAX_RS485_DATA];
  uint16_t crc;

  for (int i = 0; i < sizeof(reg_arr) / sizeof(reg_arr[0]); i++)
  {
    send[0] = m_addr;
    send[1] = 0x03;
    send[2] = reg_arr[i] / 256;
    send[3] = reg_arr[i] % 256;
    send[4] = 0;
    send[5] = 0x02;

    crc = crc_chk(send, 6);
    send[6] = crc % 256;
    send[7] = crc / 256;

    RS485Msg msg;
    msg.setData(send, 8);
    m_msg.push_back(msg);
  }

  iterator = m_msg.begin();

  baUtil::addElecNodes(this);

  return 1;
}

bool elecNodePrimevolt::set_data(uint8_t *p_data, int len)
{
  if (p_data[0] != m_addr)
  {
    return false;
  }
  int aspect_length = 7;
  uint16_t crc = crc_chk(p_data, aspect_length);
  if (p_data[1] == 0x83 || p_data[1] != 0x03)
  {
    for (size_t i = 0; i < len; i++)
    {
      printf("%02X ", p_data[i]);
      /* code */
    }

    cout << "elec primevolt response 83 error" << endl;
    return true;
  }
  if (crc % 256 != p_data[aspect_length] || crc / 256 != p_data[aspect_length + 1])
  {
    for (size_t i = 0; i < len; i++)
    {
      printf("%02X ", p_data[i]);
      /* code */
    }

    cout << "elec primevolt crc checksum error " << aspect_length << ", m_index:" << m_index << endl;
    return true;
  }
  // cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<m_index<<endl;
  // usleep(10000);
  //  float val;
  //  float *p_val;
  //  int* int_val_1;
  unsigned int float_value;
  uint8_t buf[4];

  buf[0] = p_data[6];
  buf[1] = p_data[5];
  buf[2] = p_data[4];
  buf[3] = p_data[3];
  // cout << "m_index:" << m_index << ",";
  // for (size_t i = 0; i < 4; i++)
  // {
  //   cout << static_cast<int>(p_data[6 - i]) << ",";
  // }
  // cout << endl
  //      << endl;

  float_value = (*(unsigned long *)&buf[0]) *100;
  switch (m_index)
  {
  case 0:
    kwh = (long)(*(unsigned long *)&buf[0]) *100;
    updateMessage();
    break;
    // break;
  // case 1:
  //   kw = (int)float_value;
  //   break;
  // case 2:
  //   volage = (int)float_value;
  //   break;
  // case 3:
  //   current = (int)float_value;
  //   break;
  // case 4:
  //   freq = (int)float_value;
  //   break;
  // case 5:
  //   power_factor = (int)float_value;

  //   break;
  // case 6:
  //   V_BN = (int)float_value;
  //   break;
  // case 7:
  //   V_CN = (int)float_value;
  //   break;
  // case 8:
  //   A_A = (int)float_value;
  //   break;
  // case 9:
  //   A_B = (int)float_value;
  //   break;
  // case 10:
  //   A_C = (int)float_value;

  //   break;
  // case 11:
    // kvar = (int)float_value;

  }

  m_index++;

  if (m_index >= m_msg.size())
  {
    m_index = 0;
  }

  return true;
}