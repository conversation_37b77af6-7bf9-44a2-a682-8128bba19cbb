#ifndef __WEEMAIAQNODE_HPP__
#define __WEEMAIAQNODE_HPP__
#include <json/json.h>

#include "analogNode.hpp"
class weemaIaqNode : public analogNode
{
public:
    weemaIaqNode();
    virtual ~weemaIaqNode();

    int set(Json::Value value);
    bool set_data(uint8_t* p_data,int len);
    void triggerAlarm();
    void do_di_release(int alarmdir, int alarm);
    void do_di_event(int alarmdir, int alarm);
    DIOState get_ediostate();
    bool get_di_alarm_triggered();
// private:

protected:
  void setStatusByAlarmCount();
  int alarmCount();
  void updateMessage();
  void sendMessageToBa();
// private:
  int m_id;
  int m_dio_type;

  float temperature;
  float temperature_alarm_threshold;
  int temperature_alarmdir;
  int temperature_alarm;
  bool temperature_alarm_triggered = false;
  
  float humidity;
  float humidity_alarm_threshold;
  int humidity_alarmdir;
  int humidity_alarm;
  bool humidity_alarm_triggered = false;

  float co;
  float co_alarm_threshold;
  int co_alarmdir;
  int co_alarm;
  bool co_alarm_triggered = false;

  float co2;
  float co2_alarm_threshold;
  int co2_alarmdir;
  int co2_alarm;
  bool co2_alarm_triggered = false;

  float pm01;
  float pm01_alarm_threshold;
  int pm01_alarmdir;
  int pm01_alarm;
  bool pm01_alarm_triggered = false;
  
  float pm25;
  float pm25_alarm_threshold;
  int pm25_alarmdir;
  int pm25_alarm;
  bool pm25_alarm_triggered = false;
  
  float pm10;
  float pm10_alarm_threshold;
  int pm10_alarmdir;
  int pm10_alarm;
  bool pm10_alarm_triggered = false;  

  float hcho;
  float hcho_alarm_threshold;
  int hcho_alarmdir;
  int hcho_alarm;
  bool hcho_alarm_triggered = false;
  
  float tvoc;
  float tvoc_alarm_threshold;
  int tvoc_alarmdir;
  int tvoc_alarm;
  bool tvoc_alarm_triggered = false;

  int status = 1;
  int last_update_timestamp;

  uint8_t m_buf[30];
};

#endif
