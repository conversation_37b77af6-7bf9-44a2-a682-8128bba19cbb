#include <ctime>
#include "elecNodeTatung.hpp"

#include "baUtil.hpp"
#include "utility.hpp"

using namespace std;
#define MAX_RS485_DATA 10
elecNodeTatung::elecNodeTatung()
{
  kwh = 0;
  kw = 0;
  index = 0;
  volage = 0;
  current = 0;
  freq = 0;
  power_factor = 0;

  update_kwh_timestamp = 0;
  update_timestamp = 0;
  old_kwh = 0;
}

elecNodeTatung::~elecNodeTatung()
{
}

int elecNodeTatung::set(Json::Value it)
{
  //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;
  analogNode::set(it);
  set_int_value(it, &m_id, "id");
  set_int_value(it, &index, "index");
  set_int_value(it, &m_dio_type, "dio_type");
  // set_int_value(it, &kwh, "elec_kwh");

  cout << "ZZZZZZZZZZZZZZZZZZINDEX!!!!!!!!!!!!!!!!:" << index << endl
       << endl
       << endl;
  int reg_arr[]{
      tatungElectronicMeterAddressMappingKwh[0],
      tatungElectronicMeterAddressMappingKwh[1],
      tatungElectronicMeterAddressMappingKwh[2],
      tatungElectronicMeterAddressMappingKwh[3],
      tatungElectronicMeterAddressMappingKwh[4],
      tatungElectronicMeterAddressMappingKwh[5],
      tatungElectronicMeterAddressMappingKwh[6],
      tatungElectronicMeterAddressMappingKwh[7],
      tatungElectronicMeterAddressMappingKwh[8],
      tatungElectronicMeterAddressMappingKwh[9],
      tatungElectronicMeterAddressMappingKwh[10],
      tatungElectronicMeterAddressMappingKwh[11]};
  uint8_t send[MAX_RS485_DATA];
  uint16_t crc;

  for (int i = 0; i < sizeof(reg_arr) / sizeof(reg_arr[0]); i++)
  {
    // cout << "iIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII:" << i << endl
    //      << endl;
    send[0] = m_addr;
    send[1] = 0x03;
    send[2] = reg_arr[i] / 256;
    send[3] = reg_arr[i] % 256;
    send[4] = 0;
    send[5] = 0x02;

    crc = crc_chk(send, 6);
    send[6] = crc % 256;
    send[7] = crc / 256;

    if (index == 1)
    {
      RS485Msg msg;
      msg.setData(send, 8);
      m_msg.push_back(msg);
    }
  }

  iterator = m_msg.begin();

  baUtil::addElecNodes(this);

  return 1;
}

bool elecNodeTatung::set_data(uint8_t *p_data, int len)
{
  if (p_data[0] != m_addr || index != 1)
  {
    return false;
  }

  //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<m_index<<endl;
  //usleep(10000);
  // float val;
  // float *p_val;
  // int* int_val_1;
  int int_val;
  uint8_t buf[4];
  // sleep(1);
  buf[0] = p_data[6];
  buf[1] = p_data[5];
  buf[2] = p_data[4];
  buf[3] = p_data[3];
  // cout << "m_index:" << m_index << ",";
  // for (size_t i = 0; i < 4; i++)
  // {
  //   cout << static_cast<int>(p_data[6 - i]) << ",";
  // }
  // cout << endl
  //      << endl;

  int_val = *(int *)&buf[0];
  kwhs[m_index] = (long)int_val / 1000.0 * 100.0;

  if (m_index == 0)
  {
    elecNode::setKWH(kwhs[m_index]);
    updateMessage();
  } else
  {
    baUtil::setElecChildrenNodes(m_pid, m_addr, m_index +1, kwhs[m_index]);
  }

  // cout << "~~~~~slave:"  << m_addr << ",index:" << m_index+1  << ",kwh: " << kwhs[m_index] << endl;
  // // for (size_t i = 0; i < m_msg.size(); i++)
  // // {
  // //   cout << i +1 << ":" << kwhs[i] /100.0 << " ,";
  // // }
  // // cout << endl << endl;
  
  m_index++;

  if (m_index >= m_msg.size())
  {
    m_index = 0;
  }
  // if (index == 11)
  //   return true;
  // else
  //   return false;
  return true;
}