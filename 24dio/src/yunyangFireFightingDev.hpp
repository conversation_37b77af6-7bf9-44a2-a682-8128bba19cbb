#ifndef __YUNYANGFIREFIGHTINGDEV_HPP__
#define __YUNYANGFIREFIGHTINGDEV_HPP__

#include "modbusDev.hpp"
#include <json/json.h>
class yunyangFireFightingDev : public modbusDev
{
public:
    yunyangFireFightingDev();
    virtual ~yunyangFireFightingDev();
    bool get_do_data(uint8_t *p_buf,int* p_len);
    bool get_di_data(uint8_t *p_buf,int* p_len);
    int set(Json::Value it);
    bool get_next_data(uint8_t*p_buf,int*p_len);
    // void do_di(uint8_t *p_buf);
    int m_addr;
    int db_id;
    // bool set_data(uint8_t* p_data,int len);
private:
    void set_do(int addr,int index,char value,uint8_t*p_buf,int*p_len);
    bool doneThisPoll;
    void updateMessage(int id, uint8_t floor_display_name);
    int last_update_timestamp;
};

#endif
