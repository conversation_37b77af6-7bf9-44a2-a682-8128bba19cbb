#include <ctime>
#include "elecNodeOpcDaV2.hpp"

#include "baUtil.hpp"
#include "utility.hpp"

using namespace std;
#define MAX_RS485_DATA 20
elecNodeOpcDaV2::elecNodeOpcDaV2()
{
  kwh = 0;
  kw = 0;
  volage = 0;
  current = 0;
  A_A = 0;
  A_B = 0;
  A_C = 0;
  V_BN = 0;
  V_CN = 0;
  kvar = 0;

  freq = 0;
  power_factor = 0;
  update_kwh_timestamp = 0;
  update_timestamp = 0;
  old_kwh = 0;
}

elecNodeOpcDaV2::~elecNodeOpcDaV2()
{
  cout << "opcda elec1" << endl;
}

int elecNodeOpcDaV2::set(Json::Value it)
{
  // cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;
  analogNode::set(it);
  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");

  int reg_arr[] =
      {
          10, // 0@A0 00, KWH 4 0.01kwh ulong
              // 321,   //1@01 41 psum 2 (kw total?) 0.01kw int 1
              // 305,   //2@01 31 V1 (V A-N?) 0.01v uint 2
              // 305,    //3@01 31 I Avg (巧力沒有)
              // 304,   //4@01 30@2 F (HZ) 0.01h uint 1
              // 333,   //5@01 4d PF 0.001 int 1
              // 307,  //6@01 33 V2 (V BN?) 0.01v uint 2
              // 309,   //7@01 35 V3 (V CN?) 0.01v uint 2
              // 312,   //8@01 38 I1 (A A?) 0.01a uint 2
              // 314,   //9@01 3a I2 (A B?) 0.01a uint 2
              // 316,   //10@01 3c I3 (A C?) 0.01a uint 2
              // 325    //11@01 45 qsum (kvar?) 0.01kvar int 1
      };
  uint8_t send[MAX_RS485_DATA];
  uint16_t crc;
  cout << "general opcda elec" << endl;
  send[0] = m_addr / 256;
  send[1] = m_addr % 256;
  send[2] = send[3] = send[4] = 0x00;
  send[5] = 0x06;

  send[6] = 0x01;
  send[7] = 0x03;
  send[8] = m_addr / 256;
  send[9] = m_addr % 256;

  send[10] = 0x00;
  send[11] = 50;
  RS485Msg msg;
  msg.setData(send, 12);
  m_msg.push_back(msg);

  iterator = m_msg.begin();

  baUtil::addElecNodes(this);

  return 1;
}

bool elecNodeOpcDaV2::set_data(uint8_t *p_data, int len)
{
    // cout << "opc elec set data: " << m_index << endl;
  if ((p_data[0] != m_addr / 256) || (p_data[1] % 256 != m_addr % 256))
  {
    return false;
  }  

  // cout << "opc elec: " << m_index << endl;
  uint8_t buf4[8];
  float float_value;
  double double_value;
  // float float_value = *(float *)&buf[0];
  switch (m_index)
  {
  case 0:
    buf4[0] = p_data[12];
    buf4[1] = p_data[11];
    buf4[2] = p_data[10];
    buf4[3] = p_data[9];
    float_value = *(float *)&buf4[0];
    volage = (int)(float_value * 100);

    buf4[0] = p_data[16];
    buf4[1] = p_data[15];
    buf4[2] = p_data[14];
    buf4[3] = p_data[13];
    float_value = *(float *)&buf4[0];
    V_BN = (int)(100 * float_value);

    buf4[0] = p_data[20];
    buf4[1] = p_data[19];
    buf4[2] = p_data[18];
    buf4[3] = p_data[17];
    float_value = *(float *)&buf4[0];
    V_CN = (int)(100 * float_value);


    buf4[0] = p_data[24];
    buf4[1] = p_data[23];
    buf4[2] = p_data[22];
    buf4[3] = p_data[21];
    float_value = *(float *)&buf4[0];
    A_A = (int)(100 * float_value);

    buf4[0] = p_data[28];
    buf4[1] = p_data[27];
    buf4[2] = p_data[26];
    buf4[3] = p_data[25];
    float_value = *(float *)&buf4[0];
    A_B = (int)(100 * float_value);

    buf4[0] = p_data[32];
    buf4[1] = p_data[31];
    buf4[2] = p_data[30];
    buf4[3] = p_data[29];
    float_value = *(float *)&buf4[0];
    A_C = (int)(100 * float_value);

    buf4[0] = p_data[36];
    buf4[1] = p_data[35];
    buf4[2] = p_data[34];
    buf4[3] = p_data[33];
    float_value = *(float *)&buf4[0];
    freq = (int)(100 * float_value);

    buf4[0] = p_data[40];
    buf4[1] = p_data[39];
    buf4[2] = p_data[38];
    buf4[3] = p_data[37];
    float_value = *(float *)&buf4[0];
    kwh = (long)(100 * float_value);

    // buf4[0] = p_data[44];
    // buf4[1] = p_data[43];
    // buf4[2] = p_data[42];
    // buf4[3] = p_data[41];
    // float_value = *(float *)&buf4[0];
    // kvar = (int)(100 * float_value);
    
    buf4[0] = p_data[48];
    buf4[1] = p_data[47];
    buf4[2] = p_data[46];
    buf4[3] = p_data[45];
    float_value = *(float *)&buf4[0];
    kw = (int)(100 * float_value);


    buf4[0] = p_data[52];
    buf4[1] = p_data[51];
    buf4[2] = p_data[50];
    buf4[3] = p_data[49];
    float_value = *(float *)&buf4[0];
    kvar = (int)(100 * float_value);

    buf4[0] = p_data[56];
    buf4[1] = p_data[55];
    buf4[2] = p_data[54];
    buf4[3] = p_data[53];
    float_value = *(float *)&buf4[0];
    power_factor = (int)(float_value*100) ;

    // buf4[0] = p_data[56];
    // buf4[1] = p_data[55];
    // buf4[2] = p_data[54];
    // buf4[3] = p_data[53];
    // buf4[4] = p_data[52];
    // buf4[5] = p_data[51];
    // buf4[6] = p_data[50];
    // buf4[7] = p_data[49];
    // double_value = *(double *)&buf4[0];
    // kwh = (int)(100 * double_value);

    updateMessage();
    break;
  }

  m_index++;

  if (m_index >= m_msg.size())
  {
    m_index = 0;
  }

  return true;
}