#ifndef __ELECNODESHIHLINSPM8SOLAR_HPP__
#define __ELECNODESHIHLINSPM8SOLAR_HPP__
#include <json/json.h>

#include "analogNode.hpp"
#include "elecNode.hpp"
class elecNodeShihlinSPM8Solar : public elecNode
{
public:
  elecNodeShihlinSPM8Solar();
  virtual ~elecNodeShihlinSPM8Solar();

  int set(Json::Value value);
  bool set_data(uint8_t *p_data, int len);
  void setKWH(long kwh);
  long getKWH();

  // private:
  // public:

};

#endif
