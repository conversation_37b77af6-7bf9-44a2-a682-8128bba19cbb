#include "tempNodeYonGjia3In1.hpp"
#include "baUtil.hpp"
#include "utility.hpp"
using namespace std;

tempNodeYonGjia3In1::tempNodeYonGjia3In1()
{
  m_temp = 0;
  m_humidity = 0;
  humidity_weight = 1;
}

tempNodeYonGjia3In1::~tempNodeYonGjia3In1()
{
}

int tempNodeYonGjia3In1::set(Json::Value it)
{
  analogNode::set(it);

  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");
  set_int_value(it, &status, "status");
  
  set_int_value(it, &m_humidity_alarmdir, "humidity_alarmdir");
  set_int_value(it, &m_humidity_alarm, "humidity_alarm");
  set_float_value(it, &m_humidity_alarm_threshold, "humidity_alarm_threshold");

  set_int_value(it, &m_temperature_alarmdir, "temperature_alarmdir");
  set_int_value(it, &m_temperature_alarm, "temperature_alarm");
  set_float_value(it, &m_temperature_alarm_threshold, "temperature_alarm_threshold");

  set_int_value(it, &m_co2_ppm_alarmdir, "co2_ppm_alarmdir");
  set_int_value(it, &m_co2_ppm_alarm, "co2_ppm_alarm");
  set_int_value(it, &m_co2_ppm_alarm_threshold, "co2_ppm_alarm_threshold");

  //cout << "tempNodeYonGjia3In1::set" << it << endl;
  //   int m_humidity_alarmdir;
  // int m_humidity_alarm;
  // int m_temperature_alarmdir;
  // int m_temperature_alarm;
  // int m_humidity_alarm_threshold;
  // int m_temperature_alarm_threshold;
  
  uint8_t send[8];
  send[0] = m_addr;
  send[1] = 0x03;
  send[2] = 0;
  send[3] = 0x00;
  send[4] = 0;
  send[5] = 0x04;
  int crc;

  crc = crc_chk(send, 6);
  send[6] = crc % 256;
  send[7] = crc / 256;

  RS485Msg msg;
  msg.setData(send, 8);
  m_msg.push_back(std::move(msg));

  iterator = m_msg.begin();

  return 1;
}
void tempNodeYonGjia3In1::triggerAlarm()
{

  if (m_humidity >= m_humidity_alarm_threshold && !m_humidity_alarm_triggered)
  {
    m_humidity_alarm_triggered = true;
    status = 3;
    // status = 3;
    // baUtil::do_alarm(dio_alarmdir_id, dio_alarm_id);
    do_di_event(m_humidity_alarmdir, m_humidity_alarm);
    // cout << "coSensor alarm" << endl;
  }
  else if (m_humidity < (0.95*m_humidity_alarm_threshold) && m_humidity_alarm_triggered)
  {
    m_humidity_alarm_triggered = false;
    if (!m_temperature_alarm_triggered && !m_co2_ppm_alarm_triggered) {
      status =1;
    }
    // status = 1;
    // baUtil::clear_alarm(dio_alarmdir_id, dio_alarm_id);
    do_di_release(m_humidity_alarmdir, m_humidity_alarm);
    // cout << "coSensor clear alarm" << endl;
  }


  if (m_temp >= m_temperature_alarm_threshold && !m_temperature_alarm_triggered)
  {
    m_temperature_alarm_triggered = true;
    status = 3;
    // status = 3;
    // baUtil::do_alarm(dio_alarmdir_id, dio_alarm_id);
    do_di_event(m_temperature_alarmdir, m_temperature_alarm);
    // cout << "coSensor alarm" << endl;
  }
  else if (m_temp < (0.95*m_temperature_alarm_threshold) && m_temperature_alarm_triggered)
  {
    m_temperature_alarm_triggered = false;
    if (!m_humidity_alarm_triggered && !m_co2_ppm_alarm_triggered) {
      status = 1;
    }    
    // status = 1;
    // baUtil::clear_alarm(dio_alarmdir_id, dio_alarm_id);
    do_di_release(m_temperature_alarmdir, m_temperature_alarm);
    // cout << "coSensor clear alarm" << endl;
  }

  if (m_co2_ppm >= m_co2_ppm_alarm_threshold && !m_co2_ppm_alarm_triggered)
  {
    m_co2_ppm_alarm_triggered = true;
    status = 3;
    // status = 3;
    // baUtil::do_alarm(dio_alarmdir_id, dio_alarm_id);
    do_di_event(m_co2_ppm_alarmdir, m_co2_ppm_alarm);
    // cout << "coSensor alarm" << endl;
  }
  else if (m_co2_ppm < (0.95*m_co2_ppm_alarm_threshold) && m_co2_ppm_alarm_triggered)
  {
    m_co2_ppm_alarm_triggered = false;
    if (!m_temperature_alarm_triggered && !m_humidity_alarm_triggered) {
      status =1;
    }
    // status = 1;
    // baUtil::clear_alarm(dio_alarmdir_id, dio_alarm_id);
    do_di_release(m_co2_ppm_alarmdir, m_co2_ppm_alarm);
    // cout << "coSensor clear alarm" << endl;
  }
  // if (get_di_alarm_triggered())
  // {
  //   status = 3;
  // }
  // else
  // {
  //   status = 1;
  // }
}
bool tempNodeYonGjia3In1::get_di_alarm_triggered()
{
  return m_humidity_alarm_triggered || m_temperature_alarm_triggered || m_co2_ppm_alarm_triggered;
}
DIOState tempNodeYonGjia3In1::get_ediostate()
{
  if (status == 3)
  {
    return DIOState::DIO_ALARM;
  }
  else
  {
    return DIOState::DIO_IDLE;
  }
}
void tempNodeYonGjia3In1::do_di_event(int alarmdir, int alarm)
{
  AEvent event;

  // event.name = m_name;
  // event.state = m_state;
  event.alarmdir = alarmdir;
  event.alarm = alarm;
  event.id = m_id;
  event.pid = 1;
  event.index = 0;
  event.status = get_ediostate();

  baUtil::do_di_event(&event);
}

void tempNodeYonGjia3In1::do_di_release(int alarmdir, int alarm)
{
  AEvent event;

  // event.name = m_name;
  // event.state = m_state;
  event.alarmdir = alarmdir;
  event.alarm = alarm;
  event.id = m_id;
  event.pid = 1;
  event.index = 0;
  event.status = get_ediostate();

  baUtil::do_di_release(&event);
}
bool tempNodeYonGjia3In1::set_data(uint8_t *p_data, int len)
{
  bool ret;

  ret = false;

  if (p_data[0] == m_addr)
  {
    int aspect_length = 11;
    uint16_t crc = crc_chk(p_data, aspect_length);
    if (!(p_data[1] == 0x03 && p_data[2] == 0x08 && crc % 256 == p_data[aspect_length] && crc / 256 == p_data[aspect_length + 1]))
    {
      for (size_t i = 0; i < len; i++)
      {
        printf("%02X ", p_data[i]);
        /* code */
      }

      cout << "tempNodeYonGjia3In1 response error, ";
      printf("%02X ", crc%256);
      printf("%02X ", p_data[aspect_length]);
      printf("%02X ", crc / 256);
      printf("%02X ", p_data[aspect_length + 1]);
      cout << endl;
      return true;
    }
    int temp = p_data[3] * 256 + p_data[4];
    int humidity = (p_data[5] * 256 + p_data[6]) ;
    int co2 = p_data[9] * 256 + p_data[10];
    // if (temp != m_temp || humidity != m_humidity)
    {
      m_temp = temp /10.0;
      m_humidity = humidity /10.0;
      m_co2_ppm = co2;
      updateMessage();
    }

    ret = true;

    //iterator++;
  }

  return ret;
}

void tempNodeYonGjia3In1::updateMessage()
{
  int current_timestamp = static_cast<int>(time(NULL));
  if (current_timestamp > (last_update_timestamp + 30))
  {
    last_update_timestamp = current_timestamp;
    triggerAlarm();
    sendMessageToBa();
  }
}
void tempNodeYonGjia3In1::sendMessageToBa()
{
  static int s_cnt = MAX_UPDATEMESSAGE;
  bool is_show;

  is_show = false;
  if (++s_cnt > MAX_UPDATEMESSAGE)
  {
    s_cnt = 0;
    is_show = true;
  }
  std::stringstream ss;
  ss << "/index.php?option=\"com_floor&task=sroots.update_temp2&temp=" << m_temp << "&humidity=" << m_humidity << "&co2_ppm=" << m_co2_ppm << "&id=" << m_id << "\" ";

  WSendMsg msg;

  msg.set(ss.str(), "/tmp/sendrs485tempmsg", "/tmp/SENDRS485TEMPMSG", true, is_show);
  baUtil::add_sendMessage(&msg);
}