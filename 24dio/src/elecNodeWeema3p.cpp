#include <ctime>
#include "elecNodeWeema3p.hpp"

#include "baUtil.hpp"
#include "utility.hpp"

using namespace std;
#define MAX_RS485_DATA 12
elecNodeWeema3p::elecNodeWeema3p()
{
  kwh = 0;
  kw = 0;
  volage = 0;
  current = 0;
  freq = 0;
  power_factor = 0;
  V_BN = V_CN = A_A = A_B = A_C = kvar = 0;
  // timestamp = 0;
  update_kwh_timestamp = 0;
  update_timestamp = 0;
  old_kwh = 0;
}

elecNodeWeema3p::~elecNodeWeema3p()
{
}

int elecNodeWeema3p::set(Json::Value it)
{
  // cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;
  analogNode::set(it);
  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");

  int reg_arr[] =
      {
          1182, // KWH kwh float
      };
  uint8_t send[MAX_RS485_DATA];
  uint16_t crc;

  send[0] = m_addr /256;
  send[1] = m_addr %256;
  send[2] = send[3] = send[4] = 0x00;
  send[5] = 0x06;
  send[6] = 0x01;
  send[7] = 0x03;
  send[8] = (m_addr*100) /256;
  send[9] = (m_addr*100) %256;
  send[10] = 0x00;
  send[11] = 0x64;
  // send[2] = reg_arr[i] / 256;
  // send[3] = reg_arr[i] % 256;
  // send[4] = 0;
  // if (i == 0)
  //   send[5] = 0x04;
  // else
  //   send[5] = 0x02;

  // crc = crc_chk(send, 6);
  // send[6] = crc % 256;
  // send[7] = crc / 256;

  RS485Msg msg;
  msg.setData(send, 12);
  m_msg.push_back(msg);
  // for (int i = 0; i < sizeof(reg_arr) / sizeof(reg_arr[0]); i++)
  // {
  //   send[0] = m_addr;
  //   send[1] = 0x04;
  //   send[2] = reg_arr[i] / 256;
  //   send[3] = reg_arr[i] % 256;
  //   send[4] = 0;
  //   if (i == 0)
  //     send[5] = 0x04;
  //   else
  //     send[5] = 0x02;

  //   crc = crc_chk(send, 6);
  //   send[6] = crc % 256;
  //   send[7] = crc / 256;

  //   RS485Msg msg;
  //   msg.setData(send, 8);
  //   m_msg.push_back(msg);
  // }

  iterator = m_msg.begin();

  baUtil::addElecNodes(this);

  return 1;
}

bool elecNodeWeema3p::set_data(uint8_t *p_data, int len)
{
  if (p_data[0] != (m_addr /256) || p_data[1] != (m_addr % 256))
  {
    return false;
  }
  // int aspect_length = m_index == 0 ? 11 : 7;
  // uint16_t crc = crc_chk(p_data, aspect_length);
  // if (p_data[1] == 0x83 || p_data[1] != 0x04)
  // {
  //   for (size_t i = 0; i < len; i++)
  //   {
  //     printf("%02X ", p_data[i]);
  //     /* code */
  //   }

  //   cout << "elec shihlin response 83 error" << endl;
  //   m_index = 0;
  //   return true;
  // }
  // if (crc % 256 != p_data[aspect_length] || crc / 256 != p_data[aspect_length + 1])
  // {
  //   for (size_t i = 0; i < len; i++)
  //   {
  //     printf("%02X ", p_data[i]);
  //     /* code */
  //   }

  //   cout << "elec shihlin crc checksum error " << aspect_length << ", m_index:" << m_index << endl;
  //   m_index = 0;
  //   return true;
  // }
  // cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<m_index<<endl;
  // usleep(10000);
  // float val;
  // float *p_val;
  // int* int_val_1;
  float float_value;
  uint8_t buf[4];
  size_t target_addr;
  

  // target_addr = 13;
  // buf[0] = p_data[target_addr + 1];
  // buf[1] = p_data[target_addr];
  // buf[2] = p_data[target_addr + 3];
  // buf[3] = p_data[target_addr + 2];
  // current = A_A = (int)((*(float *)&buf[0]) * 100) /1000;  
  
  target_addr = 17;
  buf[0] = p_data[target_addr + 1];
  buf[1] = p_data[target_addr];
  buf[2] = p_data[target_addr + 3];
  buf[3] = p_data[target_addr + 2];
  kw = (int)((*(float *)&buf[0]) * 100/1000);

  target_addr = 21;
  buf[0] = p_data[target_addr + 1];
  buf[1] = p_data[target_addr];
  buf[2] = p_data[target_addr + 3];
  buf[3] = p_data[target_addr + 2];
  kwh = (long)((*(float *)&buf[0]) * 100);
  kvar = 0;

  target_addr = 25;
  buf[0] = p_data[target_addr + 1];
  buf[1] = p_data[target_addr];
  buf[2] = p_data[target_addr + 3];
  buf[3] = p_data[target_addr + 2];
  volage = (int)((*(float *)&buf[0]) * 100);

  target_addr = 29;
  buf[0] = p_data[target_addr + 1];
  buf[1] = p_data[target_addr];
  buf[2] = p_data[target_addr + 3];
  buf[3] = p_data[target_addr + 2];
  V_BN = (int)((*(float *)&buf[0]) * 100);

  target_addr = 33;
  buf[0] = p_data[target_addr + 1];
  buf[1] = p_data[target_addr];
  buf[2] = p_data[target_addr + 3];
  buf[3] = p_data[target_addr + 2];
  V_CN = (int)((*(float *)&buf[0]) * 100);

  target_addr = 49;
  buf[0] = p_data[target_addr + 1];
  buf[1] = p_data[target_addr];
  buf[2] = p_data[target_addr + 3];
  buf[3] = p_data[target_addr + 2];
  current = A_A = (int)((*(float *)&buf[0]) * 100) ;

  target_addr = 43;
  buf[0] = p_data[target_addr + 1];
  buf[1] = p_data[target_addr];
  buf[2] = p_data[target_addr + 3];
  buf[3] = p_data[target_addr + 2];
  A_B = (int)((*(float *)&buf[0]) * 100);

  target_addr = 47;
  buf[0] = p_data[target_addr + 1];
  buf[1] = p_data[target_addr];
  buf[2] = p_data[target_addr + 3];
  buf[3] = p_data[target_addr + 2];
  A_C = (int)((*(float *)&buf[0]) * 100);

  target_addr = 61;
  buf[0] = p_data[target_addr + 1];
  buf[1] = p_data[target_addr];
  buf[2] = p_data[target_addr + 3];
  buf[3] = p_data[target_addr + 2];
  temperature1 = *(float *)&buf[0];

  target_addr = 65;
  buf[0] = p_data[target_addr + 1];
  buf[1] = p_data[target_addr];
  buf[2] = p_data[target_addr + 3];
  buf[3] = p_data[target_addr + 2];
  temperature2 = *(float *)&buf[0];

  target_addr = 69;
  buf[0] = p_data[target_addr + 1];
  buf[1] = p_data[target_addr];
  buf[2] = p_data[target_addr + 3];
  buf[3] = p_data[target_addr + 2];
  temperature3 = *(float *)&buf[0];

  target_addr = 73;
  buf[0] = p_data[target_addr + 1];
  buf[1] = p_data[target_addr];
  buf[2] = p_data[target_addr + 3];
  buf[3] = p_data[target_addr + 2];
  temperature4 = *(float *)&buf[0];

  target_addr = 77;
  buf[0] = p_data[target_addr + 1];
  buf[1] = p_data[target_addr];
  // buf[2] = p_data[target_addr + 3];
  // buf[3] = p_data[target_addr + 2];
  node_type = *(uint16_t *)&buf[0];

  target_addr = 79;
  buf[0] = p_data[target_addr + 1];
  buf[1] = p_data[target_addr];
  version = *(uint16_t *)&buf[0];
  // buf[2] = p_data[target_addr + 3];
  // buf[3] = p_data[target_addr + 2];
  //   int kwh;
  // int kw;
  // int volage;
  // int current;
  // int freq;
  // int power_factor;

  // int V_BN;
  // int V_CN;
  // int A_A;
  // int A_B;
  // int A_C;
  // int kvar;

  // int update_kwh_timestamp;
  // int update_timestamp;
  // int old_kwh;
  // float_value = (*(float *)&buf[0]) * 100;
  // cout << "weema outlet 1p : voltage: " << m_addr << endl;

  updateMessage();

  m_index++;

  if (m_index >= m_msg.size())
  {
    m_index = 0;
  }

  return true;
}