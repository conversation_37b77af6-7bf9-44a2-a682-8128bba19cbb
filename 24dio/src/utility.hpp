#ifndef __UTILITY_HPP__
#define __UTILITY_HPP__
#include <vector>
#include <iostream>
#include <fstream>
#include <typeinfo>
#include "wdef.h"
#include "digitNode.hpp"

#include "dioJob.hpp"
char * getNowTime();
bool cmp(digitNode &a,digitNode &b);

void set_float_value(Json::Value it,float* p_float,const char *c_str);
void set_double_value(Json::Value it,double* p_double,const char *c_str);
void set_int_value(Json::Value it,int* p_int,const char *c_str);
void set_string_value(Json::Value it,std::string* p_string,const char *c_str);
void set_uint64_value(Json::Value it, uint64_t* p_uint64, const char *c_str);
void set_int64_value(Json::Value it, int64_t* p_int64, const char *c_str);

void main_timeout(int timer,bool is_rs485);

void process_data(std::string line);

vec_string splitter(char c_patten,const char *p_sentence);

void getPingStatus(std::ifstream& file ,EDIOState status,vec_string& ip_string);

void writeIdsToJson(EDIOState status, map_int_string& ids_map,std::string& json);

std::string getCronString(Json::Value it);
void crontab_to_write(Json::Value it);
bool check_ipaddr(std::string ipaddr);
bool check_http_ret(std::string file);

void copy_file(std::string src,std::string det);
std::string readAllToJson(std::string file);
int conv_string_state(std::string str_state);
bool is_valid_time(char *p_data);

size_t write_data(void *ptr, size_t size, size_t nmemb, FILE *stream) ;
void writeToFile(std::string name,const char *p_error);
void set_normal_run();

void add_monitor_job(dioJob* job);
void save_monitor_job();
bool find_add_jobs(int id,dioJob* p_job);
void read_jobs_from_file();
void clear_jobs();
#endif
