#include "pmSensorNodeYonGjia.hpp"
#include "baUtil.hpp"
#include "utility.hpp"
using namespace std;

pmSensorNodeYonGjia::pmSensorNodeYonGjia()
{
  m_pm25 = 0;
  m_pm10 = 0;
}

pmSensorNodeYonGjia::~pmSensorNodeYonGjia()
{
}

int pmSensorNodeYonGjia::set(Json::Value it)
{
  analogNode::set(it);

  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");
  set_int_value(it, &status, "status");
  
  set_int_value(it, &m_pm25_alarmdir, "pm25_alarmdir");
  set_int_value(it, &m_pm25_alarm, "pm25_alarm");
  set_float_value(it, &m_pm25_alarm_threshold, "pm25_alarm_threshold");
  set_int_value(it, &m_pm10_alarmdir, "pm10_alarmdir");
  set_int_value(it, &m_pm10_alarm, "pm10_alarm");
  set_float_value(it, &m_pm10_alarm_threshold, "pm10_alarm_threshold");

  //cout << "pmSensorNodeYonGjia::set" << it << endl;
  //   int m_pm25_alarmdir;
  // int m_pm25_alarm;
  // int m_pm10_alarmdir;
  // int m_pm10_alarm;
  // int m_pm25_alarm_threshold;
  // int m_pm10_alarm_threshold;
  
  uint8_t send[8];
  send[0] = m_addr;
  send[1] = 0x03;
  send[2] = 0;
  send[3] = 0x02;
  send[4] = 0;
  send[5] = 0x02;
  int crc;

  crc = crc_chk(send, 6);
  send[6] = crc % 256;
  send[7] = crc / 256;

  RS485Msg msg;
  msg.setData(send, 8);
  m_msg.push_back(std::move(msg));

  iterator = m_msg.begin();

  return 1;
}
void pmSensorNodeYonGjia::triggerAlarm()
{

  if (m_pm25 >= m_pm25_alarm_threshold && !m_pm25_alarm_triggered)
  {
    m_pm25_alarm_triggered = true;
    status = 3;
    // status = 3;
    // baUtil::do_alarm(dio_alarmdir_id, dio_alarm_id);
    do_di_event(m_pm25_alarmdir, m_pm25_alarm);
    // cout << "coSensor alarm" << endl;
  }
  else if (m_pm25 < (0.95*m_pm25_alarm_threshold) && m_pm25_alarm_triggered)
  {
    m_pm25_alarm_triggered = false;
    if (!m_pm10_alarm_triggered) {
      status =1;
    }
    // status = 1;
    // baUtil::clear_alarm(dio_alarmdir_id, dio_alarm_id);
    do_di_release(m_pm25_alarmdir, m_pm25_alarm);
    // cout << "coSensor clear alarm" << endl;
  }


  if (m_pm10 >= m_pm10_alarm_threshold && !m_pm10_alarm_triggered)
  {
    m_pm10_alarm_triggered = true;
    status = 3;
    // status = 3;
    // baUtil::do_alarm(dio_alarmdir_id, dio_alarm_id);
    do_di_event(m_pm10_alarmdir, m_pm10_alarm);
    // cout << "coSensor alarm" << endl;
  }
  else if (m_pm10 < (0.95*m_pm10_alarm_threshold) && m_pm10_alarm_triggered)
  {
    m_pm10_alarm_triggered = false;
    if (!m_pm25_alarm_triggered) {
      status = 1;
    }    
    // status = 1;
    // baUtil::clear_alarm(dio_alarmdir_id, dio_alarm_id);
    do_di_release(m_pm10_alarmdir, m_pm10_alarm);
    // cout << "coSensor clear alarm" << endl;
  }
  // if (get_di_alarm_triggered())
  // {
  //   status = 3;
  // }
  // else
  // {
  //   status = 1;
  // }
}
bool pmSensorNodeYonGjia::get_di_alarm_triggered()
{
  return m_pm25_alarm_triggered || m_pm10_alarm_triggered;
}
DIOState pmSensorNodeYonGjia::get_ediostate()
{
  if (status == 3)
  {
    return DIOState::DIO_ALARM;
  }
  else
  {
    return DIOState::DIO_IDLE;
  }
}
void pmSensorNodeYonGjia::do_di_event(int alarmdir, int alarm)
{
  AEvent event;

  // event.name = m_name;
  // event.state = m_state;
  event.alarmdir = alarmdir;
  event.alarm = alarm;
  event.id = m_id;
  event.pid = 1;
  event.index = 0;
  event.status = get_ediostate();

  baUtil::do_di_event(&event);
}

void pmSensorNodeYonGjia::do_di_release(int alarmdir, int alarm)
{
  AEvent event;

  // event.name = m_name;
  // event.state = m_state;
  event.alarmdir = alarmdir;
  event.alarm = alarm;
  event.id = m_id;
  event.pid = 1;
  event.index = 0;
  event.status = get_ediostate();

  baUtil::do_di_release(&event);
}
bool pmSensorNodeYonGjia::set_data(uint8_t *p_data, int len)
{
  bool ret;

  ret = false;

  if (p_data[0] == m_addr)
  {
    int aspect_length = 7;
    uint16_t crc = crc_chk(p_data, aspect_length);
    if (!(p_data[1] == 0x03 && p_data[2] == 0x04 && crc % 256 == p_data[aspect_length] && crc / 256 == p_data[aspect_length + 1]))
    {
      for (size_t i = 0; i < len; i++)
      {
        printf("%02X ", p_data[i]);
        /* code */
      }

      cout << "pmSensorNodeYonGjia response error" << endl;
      return true;
    }
    int pm25 = p_data[3] * 256 + p_data[4];
    int pm10 = (p_data[5] * 256 + p_data[6]);

    // if (pm25 != m_pm10 || pm25 != m_pm25)
    {
      m_pm25 = pm25;
      m_pm10 = pm10;
      updateMessage();
    }

    ret = true;

    //iterator++;
  }

  return ret;
}

void pmSensorNodeYonGjia::updateMessage()
{
  int current_timestamp = static_cast<int>(time(NULL));
  if (current_timestamp > (last_update_timestamp + 30))
  {
    last_update_timestamp = current_timestamp;
    triggerAlarm();
    sendMessageToBa();
  }
}
void pmSensorNodeYonGjia::sendMessageToBa()
{
  static int s_cnt = MAX_UPDATEMESSAGE;
  bool is_show;

  is_show = false;
  if (++s_cnt > MAX_UPDATEMESSAGE)
  {
    s_cnt = 0;
    is_show = true;
  }
  std::stringstream ss;
  ss << "/index.php?option=\"com_floor&task=sroots.update_iaq&pm25=" << m_pm25 << "&pm10=" << m_pm10 << "&id=" << m_id << "\" ";

  WSendMsg msg;

  msg.set(ss.str(), "/tmp/sendrs485tempmsg", "/tmp/SENDRS485TEMPMSG", true, is_show);
  baUtil::add_sendMessage(&msg);
}