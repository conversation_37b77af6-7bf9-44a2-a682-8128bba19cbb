#ifndef __PHONEDEV_HPP__
#define __PHONEDEV_HPP__
#include <vector>
#include <fstream>
#include <json/json.h>

#include "ADevice.hpp"
#include "phoneNode.hpp"


class phoneDev : public ADevice
{
public:
    phoneDev();
   ~phoneDev();

    int set(Json::Value value);

    void doPhone();
    void main_timeout();
    bool do_recv();
    void do_state();

    void writeToFile(EDIOState status,std::ofstream& file);

    bool update(std::string msg);

protected:

private:
    void doUpdate(std::string call,EDIOState status);

    void do_sendMessage(std::vector<phoneNode>& msg,std::string callee);
    void sendMessage(std::vector<phoneNode>& items);
    void do_update_device_state(vec_string num_arr,Json::Value myjson);
    void update_device_state(std::string json);
    void save_device_status(Json::Value json);

private:
    std::vector<phoneNode> nodes;
    std::string name;

    std::string username;
    std::string passwd;
    std::string serverip;

    int online;
    int offline;

    bool is_need_scan;
};
#endif
