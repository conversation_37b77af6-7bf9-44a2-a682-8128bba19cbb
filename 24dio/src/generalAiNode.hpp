#ifndef __GENERALAINODE_HPP__
#define __GENERALAINODE_HPP__
#include <json/json.h>
#include "wdef.h"

#include "analogNode.hpp"
class generalAiNode : public analogNode
{
public:
    generalAiNode();
    virtual ~generalAiNode();

    int set(Json::Value value);
    bool set_data(uint8_t* p_data,int len);
    void triggerAlarm();
    void do_di_release(int alarmdir, int alarm);
    void do_di_event(int alarmdir, int alarm);
    DIOState get_ediostate();
    bool get_di_alarm_triggered();
// private:

protected:
  void updateMessage();
  void sendMessageToBa();
// private:
  int m_id;
  int m_dio_type;
  int m_alarmdir;
  int m_alarm;
  int alarm_threshold;
  uint8_t disalarm_percentage = 95;
  int status = 1;
  int last_update_timestamp;
  uint8_t slave_id = 1;
  double analog_value = 0;
  uint64_t analog_value_bytes;
  uint16_t offset = 0x0000;
  ModbusType modbus_type = ModbusType::ModbusTCP;
  ModbusFunctionCode modbus_function_code = ModbusFunctionCode::ReadHoldingRegisters;
  DataType data_type = DataType::Float;
  float scale = 1;  
  uint8_t decimal_digits = 2;
  bool is_big_endian = false;
  bool is_reversed_word = true;
  uint16_t header_id = 0x0000;
  uint16_t update_seconds = 30;
  // uint8_t m_buf[10];
};

#endif
