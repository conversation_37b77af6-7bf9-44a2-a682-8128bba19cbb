#include "jetecRainMeterNode.hpp"
#include "baUtil.hpp"
#include "utility.hpp"
using namespace std;

jetecRainMeterNode::jetecRainMeterNode()
{
  // m_co = -100;
  last_update_timestamp = 0;
  alarm_triggered = false;
  // alarm_threshold = 100;
  status = 1;
  int_value = 0;
  text_value = "";
  // m_temp = 0;
  // m_humidity = 0;
}

jetecRainMeterNode::~jetecRainMeterNode()
{
}

int jetecRainMeterNode::set(Json::Value it)
{
  analogNode::set(it);
  //cout << "jetecRainMeterNode:" << it << endl;
  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");
  set_int_value(it, &dio_alarm_id, "dio_alarm");
  set_int_value(it, &dio_alarmdir_id, "dio_alarmdir");
  // set_int_value(it, &alarm_threshold, "co_sensor_alarm_threshold");
  set_int_value(it, &status, "status");
  set_int_value(it, &int_value, "decimal_value");
  set_string_value(it, &text_value, "text_value");
  // cout << "coo sensodr" << it < endl;
  if (status == 3) {
    alarm_triggered = true;
  }
  uint8_t send[8];
  send[0] = m_addr;
  send[1] = 0x03;
  send[2] = 0x00;
  send[3] = 0x01;
  send[4] = 0;
  send[5] = 0x01;
  int crc;

  crc = crc_chk(send, 6);
  send[6] = crc % 256;
  send[7] = crc / 256;

  RS485Msg msg;
  msg.setData(send, 8);
  m_msg.push_back(std::move(msg));

  iterator = m_msg.begin();

  return 1;
}

bool jetecRainMeterNode::set_data(uint8_t *p_data, int len)
{
  bool ret;

  ret = false;
  if (p_data[0] == m_addr)
  {
    int newValue = p_data[3] * 256 + p_data[4]; // assign new co
    int_value = newValue;

    updateMessage();
    ret = true;

    //iterator++;
  }

  return ret;
}
void jetecRainMeterNode::updateMessage()
{
  int current_timestamp = static_cast<int>(time(NULL));
  if (current_timestamp > (last_update_timestamp + 15))
  {
    last_update_timestamp = current_timestamp;
    // triggerAlarm();
    sendMessageToBa();
  }
}
void jetecRainMeterNode::sendMessageToBa()
{
  static int s_cnt = MAX_UPDATEMESSAGE;
  bool is_show;

  is_show = false;
  if (++s_cnt > MAX_UPDATEMESSAGE)
  {
    s_cnt = 0;
    is_show = true;
  }
  std::stringstream ss;
  ss << "/index.php?option=\"com_floor&task=sroots.update_analog&decimal_value=" << int_value << "&id=" << m_id << "&text_value=" << int_value << "\" ";
  // cout << urlencode(ss.str()) << endl;
  WSendMsg msg;

  msg.set(ss.str(), "/tmp/sendrs485winddirectionmsg", "/tmp/SENDRS485WINDDIRECTIONMSG", true, is_show);
  baUtil::add_sendMessage(&msg);
}
