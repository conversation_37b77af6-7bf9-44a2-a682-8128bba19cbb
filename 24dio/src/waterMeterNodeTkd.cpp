#include <ctime>
#include "waterMeterNodeTkd.hpp"

#include "baUtil.hpp"
#include "utility.hpp"

using namespace std;
#define MAX_RS485_DATA 10
waterMeterNodeTkd::waterMeterNodeTkd()
{
  accumulateWaterFlow = 0;
  decimal_digits = -1;
  update_timestamp = 0;
}

waterMeterNodeTkd::~waterMeterNodeTkd()
{
}

int waterMeterNodeTkd::set(Json::Value it)
{
  //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;
  analogNode::set(it);
  set_int_value(it, &m_id, "id");
  set_int_value(it, &m_dio_type, "dio_type");  
  set_double_value(it, &accumulateWaterFlow, "accumulateWaterFlow");
  int reg_arr[] =
      {
          215, // decimal digits
          0  //flow
      };

  uint8_t send[MAX_RS485_DATA];
  uint16_t crc;

  for (int i = 0; i < sizeof(reg_arr) / sizeof(reg_arr[0]); i++)
  {
    send[0] = m_addr;
    send[1] = 0x03;
    send[2] = reg_arr[i] / 256;
    send[3] = reg_arr[i] % 256;
    send[4] = 0;
    send[5] = 0x02;

    crc = crc_chk(send, 6);
    send[6] = crc % 256;
    send[7] = crc / 256;

    RS485Msg msg;
    msg.setData(send, 8);
    m_msg.push_back(msg);
  }

  iterator = m_msg.begin();

  return 1;
}

bool waterMeterNodeTkd::set_data(uint8_t *p_data, int len)
{
  if (p_data[0] != m_addr)
  {
    return false;
  }

  //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<m_index<<endl;
  //usleep(10000);
  // float val;
  // float *p_val;
  // int int_val;
  // // uint8_t buf[4];

  // // buf[0] = p_data[6];
  // // buf[1] = p_data[5];
  // // buf[2] = p_data[4];
  // // buf[3] = p_data[3];
  // // p_val = (float *)&buf[0];

  // // //p_val = (float *)&buf[0];

  // // val = *p_val;

  // // int_val = val * 100;
  //cout<<int_val<<endl;
  //fflush(stdout);

  switch (m_index)
  {
  case 0:
    decimal_digits = p_data[3];    
    break;
  case 1:
    if (decimal_digits > -1)
    {
      accumulateWaterFlow = p_data[5] * 16777216 + p_data[6] * 65536 + p_data[3]*256 + p_data[4];
      accumulateWaterFlow = accumulateWaterFlow * pow(0.1,decimal_digits);
      cout << "tkd_water!!!" << accumulateWaterFlow << endl;
      updateMessage();
      decimal_digits = -1; 
    }
    else
    {
      cout << "tkd_water!!! NG" << decimal_digits << endl;
    }
    
    break;
  }

  m_index++;

  if (m_index >= m_msg.size())
  {
    m_index = 0;
  }

  return true;
}
int waterMeterNodeTkd::getIndex()
{
  return index;
}
int waterMeterNodeTkd::getPid()
{
  return m_pid;
}
void waterMeterNodeTkd::updateMessage()
{
  int current_timestamp = static_cast<int>(time(NULL));
  if ( (current_timestamp > (update_timestamp + 1 * 60)))
  {
    update_timestamp = current_timestamp;
    static int s_cnt = MAX_UPDATEMESSAGE;
    bool is_show;

    is_show = false;
    
    if (++s_cnt > MAX_UPDATEMESSAGE)
    {
      s_cnt = 0;
      is_show = true;
    }
    stringstream ss;
    ss << "/index.php?option=\"com_floor&task=sroots.update_watermeter&accumulateWaterFlow=" << accumulateWaterFlow << "&id=" << m_id << "\" ";
    // cout << ss.str() << endl;
    WSendMsg msg;
    cout << "tkd_water_update" << endl;
    msg.set(ss.str(), "/tmp/sendrs485watermetermsg", "/tmp/SENDRS485WATERMETERMSG", true, false);
    baUtil::add_sendMessage(&msg);
  }
}