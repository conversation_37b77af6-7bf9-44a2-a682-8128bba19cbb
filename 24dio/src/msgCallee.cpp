#include "msgCallee.hpp"
#include "utility.hpp"

using namespace std;

msgCallee::msgCallee()
{

}

msgCallee::~msgCallee()
{
}

int msgCallee::set(Json::Value it)
{
    for(auto it1 : it)
    {
      string number;

      set_string_value(it1,&number,"number");

      //cout<<number<<endl;
      numbers.push_back(number);
    }

    return numbers.size();
}

string msgCallee::getList()
{
  string calleelist;

  for(auto& number : numbers)
  {
    calleelist = number+"-"+calleelist;
  }

  return calleelist;
}
