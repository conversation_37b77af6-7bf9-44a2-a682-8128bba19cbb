#ifndef __ELECNODE_HPP__
#define __ELECNODE_HPP__
#include <json/json.h>

#include "analogNode.hpp"
class elecNode : public analogNode
{
public:
    elecNode();
    virtual ~elecNode();

    int set(Json::Value value);
    bool set_data(uint8_t *p_data,int len);
    void setKWH(long kwh);
    void setElecNode(long kwh, int v1, int v2, int v3, int i1, int i2, int i3, int freq, int pf, int kvar, int kh);
    long getKWH();
    int getIndex();
    int getPid();
    bool updateMessageFromBa(); 
protected:
  bool updateMessage();
public:
  int m_id;

protected:
  int index;
  //int m_id;
  int m_dio_type;

  long kwh;
  int kw;
  int volage;
  int current;
  int freq;
  int power_factor;

  float temperature1;
  float temperature2;
  float temperature3;
  float temperature4;
  int version;
  int node_type;
  int V_BN;
  int V_CN;
  int A_A;
  int A_B;
  int A_C;
  int kvar;

  int update_kwh_timestamp;
  int update_timestamp;
  long old_kwh;
};

#endif
