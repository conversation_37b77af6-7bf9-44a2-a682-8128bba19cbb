#ifndef __DIO24DEV_HPP__
#define __DIO24DEV_HPP__
#include "wdef.h"
#include <vector>
#include <json/json.h>
#include "digitNode.hpp"
#include "dioJob.hpp"
#include "ADevice.hpp"


#define MAX_DIO24 24

class dio24Dev : public ADevice
{
public:
    dio24Dev();
    virtual ~dio24Dev();
    int set(Json::Value value);

    bool do_recv();
    void do_state();
    virtual bool do_action(int parent,int device,int index,int value,int timeout);
    virtual bool do_action(int parent,int device,int index,int value,int timeout,bool return_last_state);
    void main_timeout();
    bool cancel_previous_timeout_job(int parent, int device, int index);
private:

    void do_di(uint8_t* p_buf1);
    void do_do(uint8_t* p_buf1);

    void get_di();
    void get_do();

    void set_do(int index,char value);

    bool set_do();

    void set_time();
private:

    std::vector<digitNode> m_nodes;

    std::vector<dioJob> jobs;

};

#endif
