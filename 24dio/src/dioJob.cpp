
#include "dioJob.hpp"
#include "utility.hpp"

using namespace std;

dioJob::dioJob()
{
  m_is_set = false;
  m_index = 0;
  m_value = 0;
  m_timeout = 0;
  m_return_last_state = false;
  m_last_state = 0;
  m_cancelled = false;
}

dioJob::~dioJob()
{

}

void dioJob::set(int parent,int device,int addr,int index,int value,int timeout)
{
    m_parent = parent;
    m_device = device;
    m_addr = addr;
    m_index = index;
    m_value = value;
    m_timeout = timeout;
}
void dioJob::set(int parent,int device,int addr,int index,int value,int timeout, bool return_last_state, int last_state)
{
    m_parent = parent;
    m_device = device;
    m_addr = addr;
    m_index = index;
    m_value = value;
    m_timeout = timeout;
    m_return_last_state = return_last_state;
    m_last_state = last_state;

}
bool dioJob::cancel_timeout_job(int addr, int index)
{
    // cout << "dioJOb::cancel_Timeout_job:" << addr << "/" << m_addr << ", " << index << "/" << m_index <<  endl;
  if (addr == m_addr && index == m_index)
  {     
    m_cancelled = true;
    return true;
  }
  return false;
}
void dioJob::do_timeout()
{
  if(m_timeout && !m_cancelled)
  {
    if((m_timeout%60) == 0)
      cout << "\n"<<typeid(*this).name()<<"::"<<__func__<<getNowTime()<<endl;

    m_timeout--;

    if(m_timeout == 0)
    {
      m_is_set = false;
      m_value = 0;
      // cout << "do________________________timeout:" << m_last_state << endl;
      // if (this->m_return_last_state){
      //   m_value = m_last_state;
      // } else 
      // {
      //   m_value = 0;
      // }
    }
    else{
      add_monitor_job(this);
    }
  }
}
