#include "ipDev.hpp"
#include "wdef.h"
#include "utility.hpp"

using namespace std;

ipDev::ipDev()
{


}

ipDev::~ipDev()
{

}

int ipDev::set(Json::Value it)
{
  //cout<<"\nipDev::"<<__func__<<it<<endl;
  set_int_value(it,&m_id,"id");
  set_string_value(it,&name,"name");
  set_int_value(it,&online,"online");
  set_int_value(it,&offline,"offline");

  auto it2 = it["IPs"];
  for (auto it3 : it2)
  {
      ipNode node;
      node.set(it3);

      node.online = online;
      node.offline = offline;

      nodes.push_back(std::move(node));
  }

  return nodes.size();

}

void ipDev::writeToFile(EDIOState status, ofstream& file)
{
    for(auto& node : nodes)
    {
      node.writeToFile(status, file);
    }
}


bool ipDev::update(std::string str,EDIOState status,map_int_string& ids_map)
{
  bool ret;

  ret = false;
  for(auto& node : nodes)
  {
    if(node.update(str, status,ids_map))
    {
      ret = true;
      break;
    }
  }

  return ret;
}
void ipDev::main_timeout()
{
  for(auto& node : nodes)
  {
    node.main_timeout();

  }

}
bool ipDev::do_recv()
{

}
void ipDev::do_state()
{

}
