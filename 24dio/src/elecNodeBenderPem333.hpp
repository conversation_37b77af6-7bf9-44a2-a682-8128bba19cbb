#ifndef __ELECNODEBENDERPEM333_HPP__
#define __ELECNODEBENDERPEM333_HPP__
#include <json/json.h>

#include "analogNode.hpp"
#include "elecNode.hpp"
class elecNodeBenderPem333 : public elecNode
{
public:
  elecNodeBenderPem333();
  virtual ~elecNodeBenderPem333();

  int set(Json::Value value);
  bool set_data(uint8_t *p_data, int len);
  void setKWH(long kwh);
  long getKWH();

  // private:
  // public:

};

#endif
