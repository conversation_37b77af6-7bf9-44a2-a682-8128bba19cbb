#ifndef __MODBUSDEV_HPP__
#define __MODBUSDEV_HPP__

#include <memory>
#include <vector>
#include <json/json.h>
#include "digitNode.hpp"
#include "modbusNode.hpp"
#include "dioJob.hpp"

class modbusDev
{
public:
    modbusDev();
    modbusDev(bool appendCrc, bool useCoilForDi);
    virtual ~modbusDev();
    int set(Json::Value value);
    virtual int getDevId();
    virtual bool get_di_data(uint8_t *p_buf,int* p_len);
    virtual bool get_do_data(uint8_t *p_buf,int* p_len);

    virtual void do_di(uint8_t *p_buf);
    void do_do(uint8_t *p_buf);

    virtual bool do_action(int parent,int device,int index,int value,int timeout);
    virtual bool do_action(int parent,int device,int index,int value,int timeout, bool return_last_state);
    virtual bool cancel_previous_timeout_job(int parent, int device, int index);
    //void do_state();
    bool set_do(uint8_t*p_buf,int*p_len);

    void do_doJob(int DO_addr_start,int index,int value,int timeout);
    void do_doJob(int DO_addr_start,int index,int value,int timeout, bool return_last_state, int last_state);
    void main_timeout();

    virtual bool set_data(uint8_t* p_data,int len);
    virtual bool get_next_data(uint8_t*p_buf,int*p_len);
    int getDiStartAddress();
    EState getEState();
    void init_iter();
protected:
    void doAddDIDevice(Json::Value it);
    void doAddDODevice(Json::Value it);

private:
    virtual void set_do(int addr,int index,char value,uint8_t*p_buf,int*p_len);

    bool is_analog_device();
    bool check_iter_valid();
    bool analog_next_data(uint8_t*p_buf,int*p_len);
public:
    bool useCoilForDi;
protected:
    bool appendCrc;
    int device_id;
    int DI_addr_start;
    int DI_count;

    int DO_addr_start;
    int DO_count;

    std::vector<std::shared_ptr<modbusNode>> nodes;
    std::vector<std::shared_ptr<modbusNode>>::iterator iterator;


    //std::vector<modbusNode> m_nodes;
    std::vector<dioJob> jobs;

    int dio_type;
    EState state;

};

#endif
