#include "jetecWindDirectionNode.hpp"
#include "baUtil.hpp"
#include "utility.hpp"
using namespace std;

jetecWindDirectionNode::jetecWindDirectionNode()
{
  // m_co = -100;
  last_update_timestamp = 0;
  alarm_triggered = false;
  // alarm_threshold = 100;
  status = 1;
  int_value = 0;
  text_value = "";
  // m_temp = 0;
  // m_humidity = 0;
}

jetecWindDirectionNode::~jetecWindDirectionNode()
{
}

int jetecWindDirectionNode::set(Json::Value it)
{
  analogNode::set(it);
  //cout << "jetecWindDirectionNode:" << it << endl;
  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");
  set_int_value(it, &dio_alarm_id, "dio_alarm");
  set_int_value(it, &dio_alarmdir_id, "dio_alarmdir");
  // set_int_value(it, &alarm_threshold, "co_sensor_alarm_threshold");
  set_int_value(it, &status, "status");
  set_int_value(it, &int_value, "decimal_value");
  set_string_value(it, &text_value, "text_value");
  // cout << "coo sensodr" << it < endl;
  if (status == 3) {
    alarm_triggered = true;
  }
  uint8_t send[8];
  send[0] = m_addr;
  send[1] = 0x03;
  send[2] = 0x03;
  send[3] = 0xee;
  send[4] = 0;
  send[5] = 0x01;
  int crc;

  crc = crc_chk(send, 6);
  send[6] = crc % 256;
  send[7] = crc / 256;

  RS485Msg msg;
  msg.setData(send, 8);
  m_msg.push_back(std::move(msg));

  iterator = m_msg.begin();

  return 1;
}

bool jetecWindDirectionNode::set_data(uint8_t *p_data, int len)
{
  bool ret;

  ret = false;
  if (p_data[0] == m_addr)
  {
    int newValue = p_data[3] * 256 + p_data[4]; // assign new co
    int_value = newValue;
    if (int_value >= 348 || int_value < 11)
    {
      text_value = "北(N)";
    }
    else if (int_value >= 11 && int_value <33)
    {
      text_value = "北東北(NNE)";
    }
    else if (int_value >=33 && int_value < 56)
    {
      text_value = "東北(NE)";
    }
    else if (int_value >=56 && int_value < 78)
    {
      text_value = "東東北(ENE)";
    }
    else if (int_value >=78 && int_value < 101)
    {
      text_value = "東(E)";
    }
    else if (int_value >=101 && int_value < 123)
    {
      text_value = "東東南(ESE)";
    }
    else if (int_value >=123 && int_value < 146)
    {
      text_value = "東南(SE)";
    }
    else if (int_value >=146 && int_value < 168)
    {
      text_value = "南東南(SSE)";
    }
    else if (int_value >=168 && int_value < 191)
    {
      text_value = "南(S)";
    }
    else if (int_value >=191 && int_value < 213)
    {
      text_value = "南西南(SSW)";
    }
    else if (int_value >=213 && int_value < 236)
    {
      text_value = "西南(SW)";
    }
    else if (int_value >=236 && int_value < 258)
    {
      text_value = "西西南(WSW)";
    }
    else if (int_value >=258 && int_value < 281)
    {
      text_value = "西(W)";
    }
    else if (int_value >=281 && int_value < 303)
    {
      text_value = "西西北(WNW)";
    }
    else if (int_value >=303 && int_value < 326)
    {
      text_value = "西北(NW)";
    }
    else if (int_value >=326 && int_value < 348)
    {
      text_value = "北西北(NNW)";
    }
    updateMessage();
    ret = true;

    //iterator++;
  }

  return ret;
}
void jetecWindDirectionNode::updateMessage()
{
  int current_timestamp = static_cast<int>(time(NULL));
  if (current_timestamp > (last_update_timestamp + 15))
  {
    last_update_timestamp = current_timestamp;
    // triggerAlarm();
    sendMessageToBa();
  }
}
void jetecWindDirectionNode::sendMessageToBa()
{
  static int s_cnt = MAX_UPDATEMESSAGE;
  bool is_show;

  is_show = false;
  if (++s_cnt > MAX_UPDATEMESSAGE)
  {
    s_cnt = 0;
    is_show = true;
  }
  std::stringstream ss;
  ss << "/index.php?option=\"com_floor&task=sroots.update_analog&decimal_value=" << int_value << "&id=" << m_id << "&text_value=" << urlencode(text_value) << "\" ";
  // cout << urlencode(ss.str()) << endl;
  WSendMsg msg;

  msg.set(ss.str(), "/tmp/sendrs485winddirectionmsg", "/tmp/SENDRS485WINDDIRECTIONMSG", true, is_show);
  baUtil::add_sendMessage(&msg);
}
