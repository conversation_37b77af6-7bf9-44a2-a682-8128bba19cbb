#ifndef __IPDEV_HPP__
#define __IPDEV_HPP__
#include <vector>
#include <fstream>
#include <json/json.h>

#include "ADevice.hpp"
#include "ipNode.hpp"


class ipDev : public ADevice
{
public:
    ipDev();
   ~ipDev();

    int set(Json::Value value);
    void main_timeout();
    bool do_recv();
    void do_state();

    void writeToFile(EDIOState status,std::ofstream& file);

    bool update(std::string,EDIOState status,map_int_string& ids_map);

protected:

private:

private:
    std::vector<ipNode> nodes;
    std::string name;

    int online;
    int offline;
};
#endif
