#include "yunyangFireFightingDev.hpp"
#include "utility.hpp"
#include "baUtil.hpp"

using namespace std;
yunyangFireFightingDev::yunyangFireFightingDev()
{
  this->doneThisPoll = false;
  this->last_update_timestamp = 0;
}

yunyangFireFightingDev::~yunyangFireFightingDev()
{
}
// bool yunyangFireFightingDev::set_data(uint8_t *p_data, int len)
// {
//   bool ret;

//   ret = false;
//   //cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;

//   for (auto &node : nodes)
//   {
//     if (node->m_device_id == p_data[0])
//     {
//       if (node->set_data(p_data, len))
//       {
//         ret = true;
//         break;
//       }
//     }
//   }

//   return ret;
// }
int yunyangFireFightingDev::set(Json::Value it)
{
  set_int_value(it, &db_id, "id");
  set_int_value(it, &device_id, "device_id");
  set_int_value(it, &dio_type, "dio_type");
  set_int_value(it, &m_addr, "addr");

  //cout<<"\n"<<it<<endl;
  cout << "ZZ" << it << endl;
  if (dio_type == 1)
  {
    doAddDIDevice(it);
  }
  else if (dio_type == 2)
  {
    doAddDODevice(it);
  }
  else
  {
    doAddDODevice(it);
  }

  auto it1 = it["nodes"];
  // cout << (sizeof(it["nodes"])/sizeof(&it["nodes"][0])) << endl;
  for (auto it2 : it1)
  {
    it2["pid"] = it["pid"];
    it2["did"] = it["id"];
    it2["addr"] = it["addr"];
    it2["port"] = it["port"];
    it2["pdio_type"] = it["dio_type"];
    // it2["m_device_id"] = it["device_id"];
    if (it2["enable"] == "1")
    {
      auto dev = std::make_shared<modbusNode>(); // modbusTCPDev dev;
      dev->set(it2);
      //std::cout << "elm: " << it ;//<< std::endl;

      nodes.push_back(dev);
    }
  }
  init_iter();

  return nodes.size();
}
bool yunyangFireFightingDev::get_next_data(uint8_t *p_buf, int *p_len)
{
  //  std::vector<std::shared_ptr<modbusNode>> nodes;
  // std::vector<std::shared_ptr<modbusNode>>::iterator iterator;
  // if(!check_iter_valid())
  // {
  //   iterator = nodes.begin();
  // }

  // if (iterator == nodes.end())
  // {
  //   iterator = nodes.begin();

  //   if (nodes.size() > 1)
  //   {

  //     return false;
  //   }
  // }

  // if (nodes.size() == 1)
  // {
  //   if (m_is_set == false)
  //   {
  //     m_is_set = true;
  //   }
  //   else
  //   {
  //     m_is_set = false;

  //     return false;
  //   }
  // }
  // cout << "m_device_id:" << (*iterator)->m_device_id;
  // return this->get_di_data(p_buf, p_len, (*iterator)->m_device_id);
  // iterator->getData(p_buf, p_len);
  // iterator++;

  // return true;
  return get_di_data(p_buf, p_len);
}
bool yunyangFireFightingDev::get_do_data(uint8_t *p_buf, int *p_len)
{
  // cout << DO_count << "liu" << endl;
  if (DO_count == 0)
  {
    return false;
  }

  if (modbusDev::set_do(p_buf, p_len))
  {
    // cout << "set do " << endl;
    fflush(stdout);
    return true;
  }
  p_buf[0] = 0xad;
  p_buf[1] = device_id;
  p_buf[2] = 0x01;
  for (int i = 3; i <= 12; i++)
  {
    p_buf[i] = 0;
  }
  uint8_t sum = 0;
  for (int i = 2; i <= 12; i++)
  {
    sum = sum + p_buf[i];
  }
  p_buf[13] = sum / 256;
  p_buf[14] = sum % 256;
  uint8_t xordata = 0x00;
  for (int i = 2; i <= 12; i++)
  {
    xordata = xordata ^ p_buf[i];
  }
  // int crc;
  p_buf[15] = xordata;
  // p_buf[0] = 0x7E;
  // p_buf[1] = 0x05;
  // p_buf[2] = DO_addr_start;
  // p_buf[3] = 0x21;
  // p_buf[4] = 0x00;

  // p_buf[5] = p_buf[2]^0x21^0x00^0xFF;
  // p_buf[6] = p_buf[2]+p_buf[4]+0x21+p_buf[5];
  *p_len = 16;

  return true;
}

bool yunyangFireFightingDev::get_di_data(uint8_t *p_buf, int *p_len)
{
  if (this->doneThisPoll)
  {
    this->doneThisPoll = false;
    return false;
  }
  // cout << DI_count << "liu DI:" << device_id << endl;
  if (DI_count == 0)
  {
    return false;
  }
  p_buf[0] = p_buf[1] = p_buf[2] = p_buf[3] = p_buf[4] = 0x00;  
  p_buf[5] = 0x06;
  
  
  // return false;
  // cout<<"\nyunyangFireFightingDev::get_di_data"<<endl;
  p_buf[6] = 0x01;
  p_buf[7] = 0x04;  
  p_buf[8] = (m_addr-1) / 256;
  p_buf[9] = (m_addr-1)% 256;
  // p_buf[4] = DI_count / 256;
  // p_buf[5] = DI_count % 256;
  p_buf[10] = 0;
  p_buf[11] = DI_count / 8 / 2;
  // int crc;
  // crc = crc_chk(p_buf, 12);
  // p_buf[12] = crc % 256;
  // p_buf[13] = crc / 256;
  *p_len = 12;
  this->doneThisPoll = true;
  return true;
}
// void yunyangFireFightingDev::do_di(uint8_t *p_buf)
// {
//   int index = 1;
//   for (auto &node : nodes)
//   {    
//     if (index == 1)
//     {
//       this->updateMessage(node->m_id, p_buf[0]);
//     }
//     else
//     {
//       node->do_di(DI_addr_start, p_buf);
//     }
//     index++;
//   }
// }
void yunyangFireFightingDev::set_do(int addr, int index, char value, uint8_t *p_buf, int *p_len)
{
  cout << "\n"
       << typeid(*this).name() << "::" << __func__ << endl;

  p_buf[0] = 0xad;
  p_buf[1] = DI_addr_start;
  p_buf[2] = 0x01;
  for (int i = 3; i <= 12; i++)
  {
    p_buf[i] = 0;
  }
  uint8_t sum = 0;
  for (int i = 2; i <= 12; i++)
  {
    sum = sum + p_buf[i];
  }
  p_buf[13] = sum / 256;
  p_buf[14] = sum % 256;
  uint8_t xordata = 0x00;
  for (int i = 2; i <= 12; i++)
  {
    xordata = xordata ^ p_buf[i];
  }
  // int crc;
  p_buf[15] = xordata;
  // p_buf[0] = 0x7E;
  // p_buf[1] = 0x05;
  // p_buf[2] = addr;
  // p_buf[3] = 0x21;
  // p_buf[4] = 0x84;
  // p_buf[5] = 0xFF^p_buf[2]^p_buf[3]^p_buf[4];
  // p_buf[6] = p_buf[2]+p_buf[3]+p_buf[4]+p_buf[5];

  *p_len = 16;
}
// void yunyangFireFightingDev::updateMessage(int id, uint8_t floor_display_name)
// {
//   int current_timestamp = static_cast<int>(time(NULL));
//   if (current_timestamp > (last_update_timestamp + 3))
//   {
//     last_update_timestamp = current_timestamp;
//     static int s_cnt = MAX_UPDATEMESSAGE;
//     bool is_show;

//     is_show = false;
//     if (++s_cnt > MAX_UPDATEMESSAGE)
//     {
//       s_cnt = 0;
//       is_show = true;
//     }
//     std::stringstream ss;
//     ss << "/index.php?option=\"com_floor&task=sroots.update_liuchuanelevator&id=" << id;
//     ss << "&floor_display_name=" << liuChuanElevatorFloorDisplayNames[floor_display_name];
//     ss << "\" ";
//     // cout << ss.str() << endl;
//     WSendMsg msg;
//     // cout << ss.str() << endl;
//     msg.set(ss.str(), "/tmp/sendrs485liuchuanelevatormsg", "/tmp/SENDRS485liuchuanelevatorMSG", true, is_show);
//     baUtil::add_sendMessage(&msg);
//   }
// }