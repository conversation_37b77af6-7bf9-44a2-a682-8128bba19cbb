#ifndef __SMARTLAMPNODE_HPP__
#define __SMARTLAMPNODE_HPP__
#include <json/json.h>

#include "analogNode.hpp"
class smartLampNode : public analogNode
{
public:
    smartLampNode();
    virtual ~smartLampNode();

    int set(Json::Value value);
    bool set_data(uint8_t* p_data,int len);
    void triggerAlarm();
    void do_di_release(int alarmdir, int alarm);
    void do_di_event(int alarmdir, int alarm);
    DIOState get_ediostate();
    bool get_di_alarm_triggered();
private:
  void updateMessage();
  void sendMessageToBa();
private:
  int m_id;
  int m_dio_type;
  // int m_humidity_alarmdir;
  // int m_humidity_alarm;
  // int m_temperature_alarmdir;
  // int m_temperature_alarm;
  // int m_humidity_alarm_threshold;
  // int m_temperature_alarm_threshold;
  // bool m_humidity_alarm_triggered = false;
  // bool m_temperature_alarm_triggered = false;
  int status = 1;
  int last_update_timestamp;
  float temperature1 = 0;
  float temperature2 = 0;
  float temperature3 = 0;
  float temperature4 = 0;
  float voltage = 0;
  float current = 0;
  float power = 0;
  uint64_t accumulatePower = 0;
  // int m_temp;
  // int m_humidity;

  uint8_t m_buf[10];
};

#endif
