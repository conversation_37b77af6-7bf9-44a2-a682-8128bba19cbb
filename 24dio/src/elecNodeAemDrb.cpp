#include <ctime>
#include "elecNodeAemDrb.hpp"

#include "baUtil.hpp"
#include "utility.hpp"

using namespace std;
#define MAX_RS485_DATA 10
elecNodeAemDrb::elecNodeAemDrb()
{
  kwh = 0;
  kw = 0;
  index = 0;
  volage = V_BN = V_CN = 0;
  kvar = 0;

  current = 0;
  freq = 0;
  power_factor = 0;

  update_kwh_timestamp = 0;
  update_timestamp = 0;
  old_kwh = 0;
}

elecNodeAemDrb::~elecNodeAemDrb()
{
}

int elecNodeAemDrb::set(Json::Value it)
{
  // cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<endl;
  analogNode::set(it);
  set_int_value(it, &m_id, "id");
  set_int_value(it, &index, "index");
  set_int_value(it, &m_dio_type, "dio_type");
  // set_int_value(it, &kwh, "elec_kwh");

  int reg_arr[]{
      0x1000,
      0x1400,
      0x1700};
  uint8_t send[MAX_RS485_DATA];
  uint16_t crc;

  for (int i = 0; i < sizeof(reg_arr) / sizeof(reg_arr[0]); i++)
  {
    // cout << "iIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII:" << i << endl
    //      << endl;
    send[0] = m_addr;
    send[1] = 0x03;
    send[2] = reg_arr[i] / 256;
    send[3] = reg_arr[i] % 256;
    send[4] = 0;
    if (i == 0)
    {
      send[5] = 0x20;
    }
    else
    {
      send[5] = 0x4c;
    }

    crc = crc_chk(send, 6);
    send[6] = crc % 256;
    send[7] = crc / 256;

    if (index == 1)
    {
      RS485Msg msg;
      msg.setData(send, 8);
      m_msg.push_back(msg);
    }
  }

  iterator = m_msg.begin();

  baUtil::addElecNodes(this);

  return 1;
}

bool elecNodeAemDrb::set_data(uint8_t *p_data, int len)
{
  if (p_data[0] != m_addr || index != 1)
  {
    return false;
  }
  int aspect_length = 157;
  if (m_index == 0)
  {
    aspect_length = 69;
  }
  uint16_t crc = crc_chk(p_data, aspect_length - 2);
  if (p_data[1] == 0x83 || p_data[1] != 0x03)
  {
    for (size_t i = 0; i < len; i++)
    {
      printf("%02X ", p_data[i]);
      /* code */
    }

    cout << "aem drb response 83 error" << endl;
    return true;
  }
  if (crc % 256 != p_data[aspect_length - 2] || crc / 256 != p_data[aspect_length - 1])
  {
    for (size_t i = 0; i < len; i++)
    {
      printf("%02X ", p_data[i]);
      /* code */
    }

    cout << "aem drb crc checksum error " << aspect_length << ", m_index:" << m_index << endl;
    return true;
  }
  // cout<<"\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<m_index<<endl;
  // usleep(10000);
  //  float val;
  //  float *p_val;
  //  int* int_val_1;

  int val;
  uint8_t buf[4];
  if (m_index == 0)
  {
    // sleep(1);
    buf[0] = p_data[4];
    buf[1] = p_data[3];
    buf[2] = p_data[6];
    buf[3] = p_data[5];
    freq = *(int *)&buf[0];

    buf[0] = p_data[24];
    buf[1] = p_data[23];
    buf[2] = p_data[22];
    buf[3] = p_data[21];
    volage = (*(int *)&buf[0]) * 10;

    buf[0] = p_data[28];
    buf[1] = p_data[27];
    buf[2] = p_data[26];
    buf[3] = p_data[25];
    V_BN = (*(int *)&buf[0]) * 10;

    buf[0] = p_data[32];
    buf[1] = p_data[31];
    buf[2] = p_data[30];
    buf[3] = p_data[29];
    V_CN = (*(int *)&buf[0]) * 10;
  }
  else if (m_index == 1)
  {
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    A_A = (*(int *)&buf[0]) * 10;
    buf[0] = p_data[10];
    buf[1] = p_data[9];
    buf[2] = p_data[8];
    buf[3] = p_data[7];
    A_B = (*(int *)&buf[0]) * 10;
    buf[0] = p_data[14];
    buf[1] = p_data[13];
    buf[2] = p_data[12];
    buf[3] = p_data[11];
    A_C = (*(int *)&buf[0]) * 10;

    buf[0] = p_data[154];
    buf[1] = p_data[153];
    buf[2] = p_data[152];
    buf[3] = p_data[151];
    kwh = (long)(*(int *)&buf[0]) * 10;

    buf[0] = p_data[34];
    buf[1] = p_data[33];
    buf[2] = p_data[32];
    buf[3] = p_data[31];
    kw = (*(int *)&buf[0]) * 10;

    buf[0] = p_data[50];
    buf[1] = p_data[49];
    buf[2] = p_data[48];
    buf[3] = p_data[47];
    kvar = (*(int *)&buf[0]) * 10;

    buf[0] = p_data[74];
    buf[1] = p_data[73];
    buf[2] = p_data[76];
    buf[3] = p_data[75];
    power_factor = (*(int *)&buf[0]) / 10;

    elecNode::setKWH(kwh);
    updateMessage();
  }
  else
  {
    buf[0] = p_data[6];
    buf[1] = p_data[5];
    buf[2] = p_data[4];
    buf[3] = p_data[3];
    int nA_A = (*(int *)&buf[0]) * 10;
    buf[0] = p_data[10];
    buf[1] = p_data[9];
    buf[2] = p_data[8];
    buf[3] = p_data[7];
    int nA_B = (*(int *)&buf[0]) * 10;
    buf[0] = p_data[14];
    buf[1] = p_data[13];
    buf[2] = p_data[12];
    buf[3] = p_data[11];
    int nA_C = (*(int *)&buf[0]) * 10;

    buf[0] = p_data[154];
    buf[1] = p_data[153];
    buf[2] = p_data[152];
    buf[3] = p_data[151];
    int nkwh = (*(int *)&buf[0]) * 10;

    buf[0] = p_data[34];
    buf[1] = p_data[33];
    buf[2] = p_data[32];
    buf[3] = p_data[31];
    int nkw = (*(int *)&buf[0]) * 10;

    buf[0] = p_data[50];
    buf[1] = p_data[49];
    buf[2] = p_data[48];
    buf[3] = p_data[47];
    int nkvar = (*(int *)&buf[0]) * 10;

    buf[0] = p_data[74];
    buf[1] = p_data[73];
    buf[2] = p_data[76];
    buf[3] = p_data[75];
    int npower_factor = (*(int *)&buf[0]) / 10;
    baUtil::setElecChildrenNodes(m_pid, m_addr, 2, nkwh, volage, V_BN, V_CN, nA_A, nA_B, nA_C, freq, npower_factor, nkvar, nkw);
  }

  // cout << "~~~~~slave:"  << m_addr << ",index:" << m_index+1  << ",kwh: " << kwhs[m_index] << endl;
  // // for (size_t i = 0; i < m_msg.size(); i++)
  // // {
  // //   cout << i +1 << ":" << kwhs[i] /100.0 << " ,";
  // // }
  // // cout << endl << endl;

  m_index++;

  if (m_index >= m_msg.size())
  {
    m_index = 0;
  }
  // if (index == 11)
  //   return true;
  // else
  //   return false;
  return true;
}