#include "weemaIaqNode.hpp"
#include "baUtil.hpp"
#include "utility.hpp"
using namespace std;

weemaIaqNode::weemaIaqNode()
{
  temperature = 0;
  humidity = 0;
  co = 0;
  co2 = 0;
  pm01 = 0;
  pm25 = 0;
  pm10 = 0;
  hcho = 0;
  tvoc = 0;
}

weemaIaqNode::~weemaIaqNode()
{
}

int weemaIaqNode::set(Json::Value it)
{
  analogNode::set(it);

  set_int_value(it, &m_id, "id");

  set_int_value(it, &m_dio_type, "dio_type");
  set_int_value(it, &status, "status");

  set_int_value(it, &humidity_alarmdir, "humidity_alarmdir");
  set_int_value(it, &humidity_alarm, "humidity_alarm");
  set_float_value(it, &humidity_alarm_threshold, "humidity_alarm_threshold");
  set_float_value(it, &humidity, "humidity");

  set_int_value(it, &temperature_alarmdir, "temperature_alarmdir");
  set_int_value(it, &temperature_alarm, "temperature_alarm");
  set_float_value(it, &temperature_alarm_threshold, "temperature_alarm_threshold");
  set_float_value(it, &temperature, "temp");

  set_int_value(it, &co_alarmdir, "co_alarmdir");
  set_int_value(it, &co_alarm, "co_alarm");
  set_float_value(it, &co_alarm_threshold, "co_sensor_alarm_threshold");
  set_float_value(it, &co, "co");

  set_int_value(it, &co2_alarmdir, "co2_ppm_alarmdir");
  set_int_value(it, &co2_alarm, "co2_ppm_alarm");
  set_float_value(it, &co2_alarm_threshold, "co2_ppm_alarm_threshold");
  set_float_value(it, &co2, "co2_ppm");

  set_int_value(it, &pm01_alarmdir, "pm01_alarmdir");
  set_int_value(it, &pm01_alarm, "pm01_alarm");
  set_float_value(it, &pm01_alarm_threshold, "pm01_alarm_threshold");
  set_float_value(it, &pm01, "pm01");

  set_int_value(it, &pm25_alarmdir, "pm25_alarmdir");
  set_int_value(it, &pm25_alarm, "pm25_alarm");
  set_float_value(it, &pm25_alarm_threshold, "pm25_alarm_threshold");
  set_float_value(it, &pm25, "pm25");

  set_int_value(it, &pm10_alarmdir, "pm10_alarmdir");
  set_int_value(it, &pm10_alarm, "pm10_alarm");
  set_float_value(it, &pm10_alarm_threshold, "pm10_alarm_threshold");
  set_float_value(it, &pm10, "pm10");

  set_int_value(it, &hcho_alarmdir, "hcho_alarmdir");
  set_int_value(it, &hcho_alarm, "hcho_alarm");
  set_float_value(it, &hcho_alarm_threshold, "hcho_alarm_threshold");
  set_float_value(it, &hcho, "hcho");

  set_int_value(it, &tvoc_alarmdir, "tvoc_alarmdir");
  set_int_value(it, &tvoc_alarm, "tvoc_alarm");
  set_float_value(it, &tvoc_alarm_threshold, "tvoc_alarm_threshold");
  set_float_value(it, &tvoc, "tvoc");

  // cout << "weemaIaqNode::set" << it << endl;
  //    int humidity_alarmdir;
  //  int humidity_alarm;
  //  int temperature_alarmdir;
  //  int temperature_alarm;
  //  int humidity_alarm_threshold;
  //  int temperature_alarm_threshold;

  uint8_t send[12];
  send[0] = m_addr;
  send[1] = send[2] = send[3] = send[4] = 0x00;
  send[5] = 0x06;
  send[6] = m_addr;
  send[7] = 0x03;
  send[8] = 0;
  send[9] = 0x00;
  send[10] = 0;
  send[11] = 0x0a;
  // int crc;

  // crc = crc_chk(send, 6);
  // send[6] = crc % 256;
  // send[7] = crc / 256;

  RS485Msg msg;
  msg.setData(send, 12);
  m_msg.push_back(std::move(msg));

  iterator = m_msg.begin();

  return 1;
}
int weemaIaqNode::alarmCount()
{
  // return 1;
  return temperature_alarm_triggered + humidity_alarm_triggered + co_alarm_triggered + co2_alarm_triggered + pm01_alarm_triggered + pm25_alarm_triggered + pm10_alarm_triggered + hcho_alarm_triggered + tvoc_alarm_triggered;
}
void weemaIaqNode::setStatusByAlarmCount()
{
  if (alarmCount() == 0)
  {
    status = 1;

    // cout << "alarm count : " << alarmCount() << endl;
  }
  else
  {
    status = 3;
    // cout << "temperature_alarm_triggered" << temperature_alarm_triggered << endl;
    // cout << "humidity_alarm_triggered" << humidity_alarm_triggered << endl;
    // cout << "co_alarm_triggered" << co_alarm_triggered << endl;
    // cout << "co2_alarm_triggered" << co2_alarm_triggered << endl;
    // cout << "pm01_alarm_triggered" << pm01_alarm_triggered << endl;
    // cout << "pm25_alarm_triggered" << pm25_alarm_triggered << endl;
    // cout << "pm10_alarm_triggered" << pm10_alarm_triggered << endl;
    // cout << "tvoc_alarm_triggered" << tvoc_alarm_triggered << endl;
    // cout << "hcho_alarm_triggered" << hcho_alarm_triggered << endl;
    // cout << "alarm count : " << alarmCount() << endl;
  }
}
void weemaIaqNode::triggerAlarm()
{

  if (humidity >= humidity_alarm_threshold && !humidity_alarm_triggered)
  {
    humidity_alarm_triggered = true;
    setStatusByAlarmCount();
    do_di_event(humidity_alarmdir, humidity_alarm);
  }
  else if (humidity < (0.95 * humidity_alarm_threshold) && humidity_alarm_triggered)
  {
    humidity_alarm_triggered = false;
    setStatusByAlarmCount();
    do_di_release(humidity_alarmdir, humidity_alarm);
  }

  if (temperature >= temperature_alarm_threshold && !temperature_alarm_triggered)
  {
    temperature_alarm_triggered = true;
    setStatusByAlarmCount();
    do_di_event(temperature_alarmdir, temperature_alarm);
  }
  else if (temperature < (0.95 * temperature_alarm_threshold) && temperature_alarm_triggered)
  {
    temperature_alarm_triggered = false;
    setStatusByAlarmCount();
    do_di_release(temperature_alarmdir, temperature_alarm);
  }

  if (co >= co_alarm_threshold && !co_alarm_triggered)
  {
    co_alarm_triggered = true;
    setStatusByAlarmCount();
    do_di_event(co_alarmdir, co_alarm);
  }
  else if (co < (0.95 * co_alarm_threshold) && co_alarm_triggered)
  {
    co_alarm_triggered = false;
    setStatusByAlarmCount();
    do_di_release(co_alarmdir, co_alarm);
  }

  if (co2 >= co2_alarm_threshold && !co2_alarm_triggered)
  {
    co2_alarm_triggered = true;
    setStatusByAlarmCount();
    do_di_event(co2_alarmdir, co2_alarm);
  }
  else if (co2 < (0.95 * co2_alarm_threshold) && co2_alarm_triggered)
  {
    co2_alarm_triggered = false;
    setStatusByAlarmCount();
    do_di_release(co2_alarmdir, co2_alarm);
  }

  if (pm01 >= pm01_alarm_threshold && !pm01_alarm_triggered)
  {
    pm01_alarm_triggered = true;
    setStatusByAlarmCount();
    do_di_event(pm01_alarmdir, pm01_alarm);
  }
  else if (pm01 < (0.95 * pm01_alarm_threshold) && pm01_alarm_triggered)
  {
    pm01_alarm_triggered = false;
    setStatusByAlarmCount();
    do_di_release(pm01_alarmdir, pm01_alarm);
  }

  if (pm25 >= pm25_alarm_threshold && !pm25_alarm_triggered)
  {
    pm25_alarm_triggered = true;
    setStatusByAlarmCount();
    do_di_event(pm25_alarmdir, pm25_alarm);
  }
  else if (pm25 < (0.95 * pm25_alarm_threshold) && pm25_alarm_triggered)
  {
    pm25_alarm_triggered = false;
    setStatusByAlarmCount();
    do_di_release(pm25_alarmdir, pm25_alarm);
  }

  if (pm10 >= pm10_alarm_threshold && !pm10_alarm_triggered)
  {
    pm10_alarm_triggered = true;
    setStatusByAlarmCount();
    do_di_event(pm10_alarmdir, pm10_alarm);
  }
  else if (pm10 < (0.95 * pm10_alarm_threshold) && pm10_alarm_triggered)
  {
    pm10_alarm_triggered = false;
    setStatusByAlarmCount();
    do_di_release(pm10_alarmdir, pm10_alarm);
  }

  if (tvoc >= tvoc_alarm_threshold && !tvoc_alarm_triggered)
  {
    tvoc_alarm_triggered = true;
    setStatusByAlarmCount();
    do_di_event(tvoc_alarmdir, tvoc_alarm);
  }
  else if (tvoc < (0.95 * tvoc_alarm_threshold) && tvoc_alarm_triggered)
  {
    tvoc_alarm_triggered = false;
    setStatusByAlarmCount();
    do_di_release(tvoc_alarmdir, tvoc_alarm);
  }

  if (hcho >= hcho_alarm_threshold && !hcho_alarm_triggered)
  {
    hcho_alarm_triggered = true;
    setStatusByAlarmCount();
    do_di_event(hcho_alarmdir, hcho_alarm);
  }
  else if (hcho < (0.95 * hcho_alarm_threshold) && hcho_alarm_triggered)
  {
    hcho_alarm_triggered = false;
    setStatusByAlarmCount();
    do_di_release(hcho_alarmdir, hcho_alarm);
  }
  // setStatusByAlarmCount();
  // if (get_di_alarm_triggered())
  // {
  //   status = 3;
  // }
  // else
  // {
  //   status = 1;
  // }
}
bool weemaIaqNode::get_di_alarm_triggered()
{
  return alarmCount() > 0;
}
DIOState weemaIaqNode::get_ediostate()
{
  if (status == 3)
  {
    return DIOState::DIO_ALARM;
  }
  else
  {
    return DIOState::DIO_IDLE;
  }
}
void weemaIaqNode::do_di_event(int alarmdir, int alarm)
{
  AEvent event;

  // event.name = m_name;
  // event.state = m_state;
  event.alarmdir = alarmdir;
  event.alarm = alarm;
  event.id = m_id;
  event.pid = 1;
  event.index = 0;
  event.status = get_ediostate();

  baUtil::do_di_event(&event);
}

void weemaIaqNode::do_di_release(int alarmdir, int alarm)
{
  AEvent event;

  // event.name = m_name;
  // event.state = m_state;
  event.alarmdir = alarmdir;
  event.alarm = alarm;
  event.id = m_id;
  event.pid = 1;
  event.index = 0;
  event.status = get_ediostate();

  baUtil::do_di_release(&event);
}
bool weemaIaqNode::set_data(uint8_t *p_data, int len)
{
  bool ret;

  ret = false;

  if (p_data[0] == m_addr)
  {
    int _temperature = p_data[9] * 256 + p_data[10];
    int _humidity = p_data[11] * 256 + p_data[12];
    int _tvoc = p_data[13] * 256 + p_data[14];
    int _co2 = p_data[15] * 256 + p_data[16];
    int _pm01 = p_data[17] * 256 + p_data[18];
    int _pm25 = p_data[19] * 256 + p_data[20];
    int _pm10 = p_data[21] * 256 + p_data[22];
    int _co = p_data[23] * 256 + p_data[24];
    int _hcho = p_data[25] * 256 + p_data[26];

    // if (temp != temp || humidity != humidity)
    {
      temperature = _temperature / 10.0;
      humidity = _humidity / 10.0;
      co = _co;
      co2 = _co2;
      hcho = _hcho;
      tvoc = _tvoc;
      pm01 = _pm01;
      pm25 = _pm25;
      pm10 = _pm10;
      updateMessage();
    }

    ret = true;

    // iterator++;
  }

  return ret;
}

void weemaIaqNode::updateMessage()
{
  int current_timestamp = static_cast<int>(time(NULL));
  if (current_timestamp > (last_update_timestamp + 5))
  {
    last_update_timestamp = current_timestamp;
    triggerAlarm();
    sendMessageToBa();
  }
}
void weemaIaqNode::sendMessageToBa()
{
  static int s_cnt = MAX_UPDATEMESSAGE;
  bool is_show;

  is_show = false;
  if (++s_cnt > MAX_UPDATEMESSAGE)
  {
    s_cnt = 0;
    is_show = true;
  }
  std::stringstream ss;
  ss << "/index.php?option=\"com_floor&task=sroots.update_iaq";
  ss << "&temperature=" << temperature;
  ss << "&humidity=" << humidity;
  ss << "&co=" << co;
  ss << "&co2=" << co2;
  ss << "&tvoc=" << tvoc;
  ss << "&hcho=" << hcho;
  ss << "&pm01=" << pm01;
  ss << "&pm25=" << pm25;
  ss << "&pm10=" << pm10;
  ss << "&id=" << m_id << "\" ";

  WSendMsg msg;
  // is_show = true;
  msg.set(ss.str(), "/tmp/sendrs485iaqmsg", "/tmp/SENDRS485IAQMSG", true, is_show);
  baUtil::add_sendMessage(&msg);
}