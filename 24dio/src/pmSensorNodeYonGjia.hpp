#ifndef __PMSENSORNODEYONGJIA_HPP__
#define __PMSENSORNODEYONGJIA_HPP__
#include <json/json.h>

#include "analogNode.hpp"
class pmSensorNodeYonGjia : public analogNode
{
public:
    pmSensorNodeYonGjia();
    virtual ~pmSensorNodeYonGjia();

    int set(Json::Value value);
    bool set_data(uint8_t* p_data,int len);
    void triggerAlarm();
    void do_di_release(int alarmdir, int alarm);
    void do_di_event(int alarmdir, int alarm);
    DIOState get_ediostate();
    bool get_di_alarm_triggered();
// private:

protected:
  void updateMessage();
  void sendMessageToBa();
// private:
  int m_id;
  int m_dio_type;
  int m_pm25_alarmdir;
  int m_pm25_alarm;
  int m_pm10_alarmdir;
  int m_pm10_alarm;
  float m_pm25_alarm_threshold;
  float m_pm10_alarm_threshold;
  bool m_pm25_alarm_triggered = false;
  bool m_pm10_alarm_triggered = false;
  
  int status = 1;
  int last_update_timestamp;
  float m_pm25;
  float m_pm10;

  uint8_t m_buf[10];
};

#endif
