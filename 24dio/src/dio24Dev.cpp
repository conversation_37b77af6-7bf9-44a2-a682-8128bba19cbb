#include "dio24Dev.hpp"
#include "client.h"
#include "utility.hpp"
using namespace std;

dio24Dev::dio24Dev()
{

}

dio24Dev::~dio24Dev()
{
    //cout<<"\ndioNode::"<<__func__<<endl;
}

int dio24Dev::set(Json::Value it)
{
  //cout<<it<<endl;
  ADevice::set(it);

  auto it2 = it["nodes"];
  for (auto it3 : it2)
  {
        it3["pid"] = it["pid"];
        it3["did"] = it["id"];
        digitNode node;

        //cout<<it3<<endl;
        node.set(it3);

        m_nodes.push_back(std::move(node));
  }

  dioJob job;
  bool ret = find_add_jobs(m_id,&job);

  if(ret == true)
  {
     jobs.push_back(std::move(job));
  }
  //sort(m_nodes.begin(), m_nodes.end(), cmp);
  return m_nodes.size();
}


void dio24Dev::set_time()
{
  time_t t = time(NULL);
  struct tm tm = *localtime(&t);
  printf("now: %d-%d-%d %d:%d:%d\n", tm.tm_year + 1900, tm.tm_mon + 1,tm.tm_mday, tm.tm_hour, tm.tm_min, tm.tm_sec);

  std::stringstream ss;

  ss <<"@SET DATE "<<tm.tm_year + 1900<<" "<<tm.tm_mon + 1<<" "<<tm.tm_mday<<" "<<tm.tm_wday<<" \r";
  //ss << "@SET TIME "<<tm.tm_hour<<" "<<tm.tm_min<<" "<<tm.tm_sec<<" \r";
  cout << "\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<getNowTime();
  cout << ss.str()<<endl;

  if(send_data(ss.str().c_str(),ss.str().length()))
  {
      this->m_state = EState::GET_DI_STATE;
  }

}


bool dio24Dev::do_recv()
{
    //m_buf[m_index] = 0;
    if(memcmp(&m_buf[1],"DI ",3) == 0)
    {
        do_di(&m_buf[4]);
    }
    else if(memcmp(&m_buf[1],"DO ",3) == 0)
    {
        do_do(&m_buf[4]);
    }
    else
    {
      //cout<<m_buf<<endl;
    }

    return true;

}


bool dio24Dev::set_do()
{
    bool ret;

    ret = false;

    if(jobs.size() == 0)
        return false;

    for(vector<dioJob>::iterator iter=jobs.begin(); iter!=jobs.end();iter++ )
    {
         if(iter->m_is_set == false)
         {
             ret = true;
             iter->m_is_set = true;

              set_do(iter->m_index,iter->m_value);

              if(iter->m_timeout == 0)
              {
                  iter = jobs.erase(iter);

              }
              else
              {
                ++iter;
              }
              break;
          }
          else
          {
            ++iter;
          }

    }

    return ret;
}
void dio24Dev::do_state()
{
    if(set_do())
    {
      return ;
    }
    switch(this->m_state)
    {
        case EState::GET_DATE_TIME_STATE:
        set_time();
        break;
        case EState::CLR_DO_STATE:
        //clr_do();
        break;
        case EState::SET_DO_STATE:
        //set_do();
        break;
        case EState::GET_DI_STATE:
        get_di();
        break;
        case EState::GET_DO_STATE:
        get_do();
        break;

        default:
        get_di();
        break;
    }
}


void dio24Dev::do_di(uint8_t* p_buf)
{
    string data = (char*) m_buf;
    for(auto& node : m_nodes)
    {
      node.m_buf_from_dio24dev = data;
      node.do_di(p_buf);

    }

}


void dio24Dev::set_do(int index,char value)
{
     std::stringstream ss;
     char dio_value[9] = {0};

     memset(dio_value,'0',8);

     //ss <<"@SET DATE "<<tm.tm_year + 1900<<" "<<tm.tm_mon + 1<<" "<<tm.tm_mday<<" "<<tm.tm_wday<<" \r";

     dio_value[index-17] = '1';
     if(value == 1)
     {
         ss << "@SET DO " << dio_value << " \r";
      }
      else
      {
        ss << "@CLR DO " << dio_value << " \r";
      }
      cout << "\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<getNowTime();
      cout << ss.str()<<endl;
      fflush(stdout);

      if(send_data(ss.str().c_str(),ss.str().length()))
      {
          this->m_state = EState::GET_DO_STATE;
      }

}


void dio24Dev::get_di()
{
  std::stringstream ss;

  //ss <<"@SET DATE "<<tm.tm_year + 1900<<" "<<tm.tm_mon + 1<<" "<<tm.tm_mday<<" "<<tm.tm_wday<<" \r";
  ss << "@GET DI \r";

  if(send_data(ss.str().c_str(),ss.str().length()))
  {
      this->m_state = EState::GET_DO_STATE;
  }

}

void dio24Dev::get_do()
{
    std::stringstream ss;

    //ss <<"@SET DATE "<<tm.tm_year + 1900<<" "<<tm.tm_mon + 1<<" "<<tm.tm_mday<<" "<<tm.tm_wday<<" \r";
    ss << "@GET DO \r";


    if(send_data(ss.str().c_str(),ss.str().length()))
    {
        this->m_state = EState::GET_DI_STATE;
    }

}
void dio24Dev::do_do(uint8_t* p_buf)
{
    int start;

    //cout<<"dioNode::do_do "<<p_buf<<endl;
    //fflush(stdout);
    start = 16;
    uint8_t mybuf[MAX_DIO24];

    memcpy(&mybuf[start],p_buf,8);
    for(auto& node : m_nodes)
    {
      node.do_do(mybuf);

    }

}
bool dio24Dev::cancel_previous_timeout_job(int parent, int device, int index)
{
  bool ret;

  ret = false;

  if(jobs.size() == 0)
      return false;
      
  for(vector<dioJob>::iterator iter=jobs.begin(); iter!=jobs.end();iter++ )
  {
    if (iter->cancel_timeout_job(0, index))
    {
      // iter = jobs.erase(iter);
      ret = true;
      // break;
    }
  }
  // for(vector<dioJob>::iterator iter=jobs.begin(); iter!=jobs.end();iter++ )
  // {
  //   if (iter->m_cancelled)
  //   {
  //     iter = jobs.erase(iter);
  //   }
      
  // }
  return ret;
}
bool dio24Dev::do_action(int parent,int device,int index,int value,int timeout,bool return_last_state)
{
    bool ret;

    ret = false;
    if(device == m_id)
    {
        dioJob job;
        cout << "\n"<<typeid(*this).name()<<"::"<<__func__<<" "<<endl;
        fflush(stdout);
        job.set(parent,device,0,index,value,timeout);
        jobs.push_back(std::move(job));

        ret = true;
    }

    return ret;
}
bool dio24Dev::do_action(int parent,int device,int index,int value,int timeout)
{
    bool ret;

    ret = false;
    if(device == m_id)
    {
        dioJob job;
        cout << "\n"<<typeid(*this).name()<<"::"<<__func__<<" 1"<<endl;
        fflush(stdout);
        job.set(parent,device,0,index,value,timeout);
        jobs.push_back(std::move(job));

        ret = true;
    }

    return ret;
}
void dio24Dev::main_timeout()
{
  if(jobs.size() == 0)
      return ;

  for(auto& job : jobs)
  {
    job.do_timeout();

  }
}
