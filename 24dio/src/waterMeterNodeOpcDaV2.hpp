#ifndef __WATERMETERNODEOPCDAV2_HPP__
#define __WATERMETERNODEOPCDAV2_HPP__
#include <json/json.h>

#include "analogNode.hpp"
class waterMeterNodeOpcDaV2 : public analogNode
{
public:
  waterMeterNodeOpcDaV2();
  virtual ~waterMeterNodeOpcDaV2();

  int set(Json::Value value);
  bool set_data(uint8_t *p_data, int len);
  int getIndex();
  int getPid();

protected:
  void updateMessage();

protected:
  int index;
  int m_id;
  int m_dio_type;
  int update_timestamp;
  double accumulateWaterFlow;
  int decimal_digits;
};

#endif
