#ifndef __IPNODE_HPP__
#define __IPNODE_HPP__
#include "wdef.h"
#include <vector>
#include <json/json.h>
#include <fstream>

class ipNode
{
public:
    ipNode();
    virtual ~ipNode();
    int set(Json::Value value);

    void writeToFile(EDIOState status, std::ofstream& file);

    bool update(std::string str,EDIOState status,map_int_string& ids_map);

    void main_timeout();
public:
  int online;
  int offline;

private:
    int id;
    std::string info;
    std::string note;

    int online_cnt;
    int offline_cnt;

    EDIOState state;
};

#endif
