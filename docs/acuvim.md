# Git Commit 比較分析報告
**分析時間：** 2024-12-19

## Commit 資訊
- **Commit A (參考基準):** 801a33e875a851a19017912aac9dc02331186961
  - 作者: <PERSON> <<EMAIL>>
  - 日期: 2022-10-25 10:05:57 +0800
  - 訊息: 新增巧力CIC BAW1A&2A電表

- **Commit B (比較對象):** e3128b41e2fe3cb3d8291dfe733e556a3ca74439
  - 作者: <PERSON> <<EMAIL>>
  - 日期: 2024-01-16 14:52:23 +0800
  - 訊息: add acuvim elec meter
  - 標籤: v1.0.66

## Commit A 修改內容 (巧力CIC BAW1A&2A電表)

### C++ 後端修改
1. **24dio/CMakeLists.txt**
   - 新增 `add_library(MyLibrary ./src/elecNodeCicBaw1a2a.cpp)`

2. **24dio/src/elecNodeCicBaw1a2a.cpp** (新檔案, 138行)
   - 實作巧力CIC BAW1A&2A電表的通訊協定
   - 支援 Modbus RTU 通訊
   - 讀取累計用電量 (kwh)
   - 使用暫存器位址 0x0006，讀取 2 個暫存器

3. **24dio/src/elecNodeCicBaw1a2a.hpp** (新檔案, 23行)
   - 定義 elecNodeCicBaw1a2a 類別標頭檔

4. **24dio/src/modbusDev.cpp**
   - 在 `is_analog_device()` 函數中新增 `CIC_BAW1A2A_ELEC_DEVICE` 支援

5. **24dio/src/modbusNode.cpp**
   - 新增 `#include "elecNodeCicBaw1a2a.hpp"`

6. **24dio/src/wdef.h**
   - 新增設備類型定義 `CIC_BAW1A2A_ELEC_DEVICE = 36`

### PHP 前端修改
7. **web/com_elec-1.0.0/site/helpers/elec.php**
   - 新增 `$elec_device_cicbaw1a2a = 36`
   - 在電表設備陣列中加入新設備類型

8. **web/com_electronic_meter_history-1.0.0/site/helpers/electronic_meter_history.php**
   - 在設備類型查詢中新增 36

9. **web/com_floor-1.0.0/site/helpers/floor.php**
   - 在多個電表相關查詢中新增設備類型 36
   - 更新 `getElecMeterDevices()` 函數

10. **web/com_floor-1.0.0/site/views/sroots/tmpl/default.php**
    - 新增註解的電表資料處理程式碼

11. **web/com_myaccount-1.0.0/site/helpers/myaccount.php**
    - 新增 `$elec_cicbaw1a2a = 36`
    - 在電表設備陣列中加入新設備類型

12. **web/com_top-1.0.0/site/helpers/top.php**
    - 新增 `$dio_elec_cicbaw1a2a_dio_type = 36`
    - 在設備類型陣列中新增 `36=>'巧力BAW-1A2A'`

13. **web/com_top-1.0.0/site/helpers/toputility.php**
    - 新增 `$rs485_elec_device_cicbaw1a2a = 36`
    - 在電表設備陣列中加入新設備類型

## Commit B 修改內容 (Acuvim電表)

### C++ 後端修改
1. **24dio/CMakeLists.txt**
   - 新增 `add_library(MyLibrary ./src/elecNodeAcuvim.cpp)`

2. **24dio/src/elecNodeAcuvim.cpp** (新檔案, 223行)
   - 實作 Acuvim 電表的通訊協定
   - 支援 Modbus RTU 通訊
   - 讀取多種電力參數：頻率、電壓、電流、功率、功率因數
   - 使用多個暫存器位址：16384(頻率)、16394(電壓)、16402(電流)、16418(功率)、16426(無功功率)、16442(功率因數)

3. **24dio/src/elecNodeAcuvim.hpp** (新檔案, 23行)
   - 定義 elecNodeAcuvim 類別標頭檔

4. **24dio/src/modbusDev.cpp**
   - 在 `is_analog_device()` 函數中新增 `ACUVIM_ELEC_DEVICE` 支援

5. **24dio/src/modbusNode.cpp**
   - 新增 `#include "elecNodeAcuvim.hpp"`
   - 新增 Acuvim 設備的建立邏輯
   - 移除了許多註解的 "CICCCCCCCCCCCCCCCCCC" 偵錯訊息

6. **24dio/src/wdef.h**
   - 新增設備類型定義 `ACUVIM_ELEC_DEVICE = 43`

### PHP 前端修改
7. **web/com_elec-1.0.0/site/helpers/elec.php**
   - 新增 `$elec_device_acuvim = 43`
   - 在電表設備陣列中加入新設備類型

8. **web/com_electronic_meter_history-1.0.0/site/helpers/electronic_meter_history.php**
   - 在設備類型查詢中新增 43

9. **web/com_floor-1.0.0/site/helpers/floor.php**
   - 新增 `$rs485_elec_devic_acuvim = 43`
   - 在多個電表相關查詢中新增設備類型 43
   - 更新 `getElecMeterDevices()` 函數

10. **web/com_myaccount-1.0.0/site/helpers/myaccount.php**
    - 新增 `$elec_acuvim = 43`
    - 在電表設備陣列中加入新設備類型

11. **web/com_top-1.0.0/media/app/dio-app**
    - 更新二進位執行檔

12. **web/com_top-1.0.0/site/helpers/top.php**
    - 新增 `$dio_elec_acuvim_dio_type = 43`
    - 在設備類型陣列中新增 `43=>'Acuvim電表'`

13. **web/com_top-1.0.0/site/helpers/toputility.php**
    - 新增 `$rs485_elec_device_acuvim = 43`
    - 在電表設備陣列中加入新設備類型

## 比較分析結果

### Commit B 相對於 Commit A 的完整性檢查

✅ **Commit B 已完整實作所有必要功能，沒有遺漏的部分**

### 主要差異點：

1. **設備類型 ID 不同**
   - Commit A: 使用 ID 36 (CIC_BAW1A2A_ELEC_DEVICE)
   - Commit B: 使用 ID 43 (ACUVIM_ELEC_DEVICE)

2. **通訊協定複雜度**
   - Commit A: 較簡單，只讀取累計用電量 (kwh)
   - Commit B: 較複雜，讀取多種電力參數 (頻率、電壓、電流、功率、功率因數)

3. **程式碼品質改善**
   - Commit B 移除了許多偵錯註解，程式碼更乾淨

4. **二進位檔案更新**
   - Commit B 包含了更新的執行檔 `dio-app`

### 結論
Commit B (Acuvim電表) 的實作完整度與 Commit A (巧力CIC BAW1A&2A電表) 相當，並且在某些方面更為完善：
- 支援更多電力參數讀取
- 程式碼更乾淨
- 包含執行檔更新

**沒有發現 Commit B 遺漏的功能或檔案修改。**